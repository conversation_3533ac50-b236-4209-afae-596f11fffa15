import streamlit as st

def main():
    st.set_page_config(
        page_title="梅斯小智用户访问行为交互平台 - 帮助手册",
        layout="centered",
        initial_sidebar_state="expanded"
    )

    st.title("梅斯小智用户访问行为交互平台 - 帮助手册")

    # 创建一个选项卡界面，方便用户浏览不同的帮助内容
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["系统概述", "登录指南", "功能介绍", "常见问题", "操作指南"])

    with tab1:
        st.header("系统概述")
        st.markdown("""
        ### 什么是梅斯小智用户访问行为交互平台？

        梅斯小智用户访问行为交互平台是一个专为运营人员设计的数据分析工具，帮助您通过简单的对话方式了解用户的访问行为数据。
        
        无需编写复杂的SQL查询语句，您只需用自然语言提问，系统就能自动生成相应的数据分析结果，包括表格和图表展示。

        ### 主要特点

        - **自然语言交互**：使用日常用语提问，无需技术背景
        - **数据可视化**：自动生成表格和图表，直观展示数据
        - **灵活查询**：支持多种数据分析角度，满足不同运营需求
        - **实时更新**：数据源可定期更新，确保分析结果及时有效
        """)

    with tab2:
        st.header("登录指南")
        st.markdown("""
        ### 如何访问系统

        1. 系统访问需要有效的访问令牌（token）
        2. 访问链接格式为：`http://系统地址/?token=您的访问令牌`
        3. 如果您没有访问令牌，请联系系统管理员获取

        ### 权限说明

        - 本系统仅对具有特定权限的用户开放
        - 如遇到"无访问权限"或"权限验证失败"等提示，请联系系统管理员
        """)

        st.warning("请勿将您的访问令牌分享给他人，以免造成数据安全风险！")

    with tab3:
        st.header("功能介绍")
        st.markdown("""
        ### 主要功能

        1. **自然语言查询**：通过聊天框输入问题，获取数据分析结果
        2. **数据展示**：以表格形式展示查询结果
        3. **数据可视化**：根据数据特点自动生成适合的图表
        4. **示例问题**：提供常用问题示例，点击即可查询
        5. **数据源更新**：手动更新数据源，获取最新数据

        ### 侧边栏设置

        侧边栏提供了多种显示选项，您可以根据需要开启或关闭：

        - **显示SQL**：查看系统生成的SQL查询语句
        - **显示表格**：以表格形式展示数据
        - **显示Plotly代码**：查看图表生成代码（技术人员使用）
        - **显示图表**：展示数据可视化图表
        - **显示概括**：提供数据分析结果的文字概括
        - **显示追问**：系统推荐的后续问题
        - **独立提问**：每次提问作为独立会话处理
        - **新会话**：开始一个全新的会话
        - **更新数据源**：手动更新数据，获取最新信息
        """)

        st.image("https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png", width=200)

    with tab4:
        st.header("常见问题")
        
        with st.expander("如何开始使用系统？"):
            st.markdown("""
            1. 使用包含有效token的链接访问系统
            2. 在聊天输入框中输入您的问题
            3. 系统会自动处理您的问题并返回结果
            4. 您也可以点击"点击显示提问示列"按钮，选择预设问题
            """)
            
        with st.expander("我可以问哪些类型的问题？"):
            st.markdown("""
            您可以询问与用户访问行为相关的各种问题，例如：
            
            - 过去一周的用户访问量是多少？
            - 哪个应用的访问量最高？
            - 移动端和PC端的用户比例是多少？
            - 用户访问高峰时段是什么时候？
            - 不同用户类型的访问行为有何差异？
            """)
            
        with st.expander("为什么我的问题没有得到正确回答？"):
            st.markdown("""
            可能的原因包括：
            
            1. 问题表述不够清晰或具体
            2. 所查询的数据不在系统数据范围内
            3. 系统理解能力有限，无法处理过于复杂的问题
            
            建议：
            - 使用简洁明了的语言
            - 一次只问一个具体问题
            - 参考系统提供的示例问题
            """)
            
        with st.expander("数据多久更新一次？"):
            st.markdown("""
            系统数据默认每小时可以更新一次。您可以通过侧边栏的"更新数据源"按钮手动触发更新。
            
            请注意，两次更新之间需要间隔至少1小时。
            """)
            
        with st.expander("如何导出查询结果？"):
            st.markdown("""
            目前系统不直接提供导出功能，但您可以：
            
            1. 使用浏览器的截图功能保存表格或图表
            2. 复制表格内容到Excel或其他工具
            3. 联系技术人员获取原始数据
            """)

    with tab5:
        st.header("操作指南")
        st.markdown("""
        ### 数据结构说明

        为帮助您更好地理解系统中的数据，以下是系统使用的主要数据表结构：

        **梅斯小智用户访问行为数据表**

        | 字段名称 | 字段说明 | 可能的值 |
        |---------|---------|----------|
        | 应用名 | 用户访问的应用名称 | 各种应用名称 |
        | 用户ID | 用户的唯一标识号 | 数字ID |
        | 用户类型 | 用户来源平台 | MedSci xAI、主站、Facebook、Google |
        | 行为 | 用户在系统中的行为 | 聊天互动、访问首页 |
        | 设备类型 | 用户访问使用的设备 | PC端、移动端 |
        | 执行时长 | 接口响应时间(毫秒) | 数字(如200、500等) |
        | 结果码 | 访问结果状态 | 0(成功)、大于0(失败) |
        | 访问时间 | 用户访问的具体时间 | 日期时间格式(如2025-01-01 12:30:45) |

        **提问技巧**

        基于上述数据结构，您可以从多个维度分析用户行为：

        - **应用维度**：比较不同应用的访问情况
        - **用户维度**：分析不同来源用户的行为差异
        - **设备维度**：了解PC端与移动端的使用情况
        - **时间维度**：分析访问高峰期和低谷期
        - **性能维度**：查看执行时长异常的情况
        - **成功率维度**：分析访问成功与失败的比例

        **示例问题**

        - "哪个应用在移动端的平均执行时长最长？"
        - "来自Facebook的用户主要使用什么设备访问？"
        - "过去一周结果码不为0的访问占比是多少？"
        - "每天的访问高峰时段是什么时候？"
        - "哪些用户在多个应用上都有访问记录？"
        """)

        st.info("如果您在使用过程中遇到任何问题，请联系系统管理员获取帮助。")

if __name__ == "__main__":

    main()