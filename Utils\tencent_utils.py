import sseclient
import requests
import json
from .common_utils import CommonUtils
from qcloud_cos import CosConfig
from qcloud_cos import CosS3Client
import hmac
import hashlib
import time
import base64
from urllib.parse import quote

class TencentUtils:
    BOT_APP_KEY = "UNPIMaaR"  # 机器人密钥，不是BotBizId (从运营接口人处获取)
    BASE_URL = "https://wss.lke.cloud.tencent.com/v1/qbot/chat/sse"

    COS_CONFIG = {
        "Region": "ap-shanghai",
        "SecretId": "AKIDuhJxZHtMm6IXPI6oLm1FPL71ukcPAtZC",
        "SecretKey": "shXTNG5kL0fQ5dE8ymY9dFoOAru17HZp",
        "Bucket": "medsci-web-resources-**********",
        "TempPath": "ai-write/pdf-img-temp/",
        "Path": "ai-write/file/",
        "BaseUrl": "https://static.medsci.cn/",
    }

    APAAS_CONFIG = {
        "appkey": "ap-shanghai",
        "accesstoken": "AKIDuhJxZHtMm6IXPI6oLm1FPL71ukcPAtZC"
    }

    @staticmethod
    def get_cos_path(is_temp:bool=False):
        basePath = TencentUtils.COS_CONFIG['TempPath'] if is_temp else TencentUtils.COS_CONFIG['Path']
        dir = CommonUtils.get_time_format_or_default(format='%Y%m%d')
        return f"{basePath}{dir}/"

    @staticmethod
    def chat_sse_client(keyword:str, api_key:str='', sid: str='', visitor_id: str = CommonUtils.MEDSCI_XAI):
        """ 
        创建sse客户端 
        sid: 访客会话 ID（外部系统提供，需确认不同的访客使用不同的 ID）
        visitor_id: 访客 ID（外部系统提供，需确认不同的访客使用不同的 ID）
        """
        req_data = {
            "content": keyword,
            "bot_app_key": api_key if api_key else TencentUtils.BOT_APP_KEY,
            "visitor_biz_id": visitor_id if api_key else CommonUtils.MEDSCI_XAI,
            "session_id": 'a2ab99d4-343b-4e52-b379-70239e2e1d84',  # sid if sid else CommonUtils.get_uuid(),
            "streaming_throttle": 1  # 节流控制
        }

        cont = ''
        try:
            resp = requests.post(TencentUtils.BASE_URL, data=json.dumps(req_data),
                                stream=True, headers={"Accept": "text/event-stream"})
            # print(f"resp:{resp.text}")
            client = sseclient.SSEClient(resp)
            for ev in client.events():
                # print(f'event:{ev.event}, "data:"{ev.data}')
                data = json.loads(ev.data)
                if ev.event == "reply":
                    if data["payload"]["is_from_self"]:  # 自己发出的包
                        # print(f'is_from_self, event:{ev.event}, "content:"{data["payload"]["content"]}')
                        print('is_from_self')
                    elif data["payload"]["is_final"]:  # 服务端event传输完毕；服务端的回复是流式的，最后一条回复的content，包含完整内容
                        # print(f'is_final, event:{ev.event}, "content:"{data["payload"]["content"]}')
                        cont = data["payload"]["content"]
                else:
                    # print(f'event:{ev.event}, "data:"{ev.data}')
                    # 也可以按需要解析其他数据
                    print(f'event:{ev.event}')
                    
        except Exception as e:
            print(e)

        return cont
     
    @staticmethod
    def upload_file_to_cos(local_file_path:str, cos_file_path:str):
        """
        上传文件到腾讯云 COS
        :param local_file_path: 本地文件路径
        :param cos_file_path: COS存储路径
        """
        # 初始化 COS 配置
        config = CosConfig(
            Region=TencentUtils.COS_CONFIG['Region'], 
            SecretId=TencentUtils.COS_CONFIG['SecretId'], 
            SecretKey=TencentUtils.COS_CONFIG['SecretKey']
        )
        client = CosS3Client(config)

        try:
            response = client.upload_file(
                Bucket=TencentUtils.COS_CONFIG['Bucket'],
                LocalFilePath=local_file_path,
                Key=cos_file_path,
                PartSize=10,
                MAXThread=10,
                EnableMD5=False
            )
            
            return TencentUtils.COS_CONFIG['BaseUrl'] + cos_file_path
        except Exception as e:
            raise e
        
    @staticmethod
    def GenSignature(signing_content, access_token) -> str:
        """
        用户可以在函数内部生成时间戳, 只需要传入appkey和accessToken即可获取访问接口所需的公共参数和签名
        """
        # 计算 HMAC-SHA256 值
        h = hmac.new(access_token.encode(), signing_content.encode(), hashlib.sha256)

        # 将 HMAC-SHA256 值进行 Base64 编码
        hash_in_base64 = base64.b64encode(h.digest()).decode()

        # URL encode
        encode_sign = quote(hash_in_base64)

        # 拼接签名
        signature = f"&signature={encode_sign}"

        return signature

    @staticmethod
    def GenReqURL(parameter, access_token, base_url) -> str:
        # 按字典序拼接待计算签名的字符串
        signing_content = '&'.join(f'{k}={parameter[k]}' for k in sorted(parameter.keys()))

        # 计算签名
        signature = TencentUtils.GenSignature(signing_content, access_token)

        # 拼接访问接口的完整 URL
        return f'{base_url}?{signing_content}{signature}'

    @staticmethod
    def get_sign_url(url: str) -> str:
        parameter = {
            'appkey': TencentUtils.APAAS_CONFIG['appkey'],
            'timestamp': int(time.time())
        }

        return TencentUtils.GenReqURL(parameter, TencentUtils.APAAS_CONFIG['accesstoken'], url)
    
    @staticmethod
    def getCosClient(secretId:str, secretKey:str, region:str = 'ap-shanghai'):
        # 初始化 COS 配置
        config = CosConfig(
            Region = region,
            SecretId = secretId,
            SecretKey = secretKey
        )
        client = CosS3Client(config)
        return client

    @staticmethod
    def upload_file_to_cos_with_signed_url(local_file_path: str, cos_file_path: str, expire_seconds: int = 3600):
        """
        上传文件到腾讯云 COS 并获取签名的访问地址
        :param local_file_path: 本地文件路径
        :param cos_file_path: COS存储路径
        :param expire_seconds: 签名URL过期时间（秒），默认1小时
        :return: dict 包含普通URL和签名URL
        """
        # 初始化 COS 配置
        secretId = CommonUtils.get_env('COS_PRIVATE_SecretId', TencentUtils.COS_CONFIG['SecretId'])
        secretKey = CommonUtils.get_env('COS_PRIVATE_SecretKey', TencentUtils.COS_CONFIG['SecretKey'])
        client = TencentUtils.getCosClient(secretId, secretKey)

        try:
            # 上传文件
            response = client.upload_file(
                Bucket=TencentUtils.COS_CONFIG['Bucket'],
                LocalFilePath=local_file_path,
                Key=cos_file_path,
                PartSize=10,
                MAXThread=10,
                EnableMD5=False
            )

            # 生成签名URL
            signed_url = client.get_presigned_url(
                Method='GET',
                Bucket=TencentUtils.COS_CONFIG['Bucket'],
                Key=cos_file_path,
                Expired=expire_seconds
            )

            # 普通访问URL
            normal_url = TencentUtils.COS_CONFIG['BaseUrl'] + cos_file_path

            return {
                'normal_url': normal_url,
                'signed_url': signed_url,
                'cos_path': cos_file_path,
                'expire_seconds': expire_seconds
            }

        except Exception as e:
            raise e

    @staticmethod
    def get_presigned_url(cos_file_path: str, expire_seconds: int = 3600):
        """
        为已存在的COS文件生成签名访问地址
        :param cos_file_path: COS存储路径
        :param expire_seconds: 签名URL过期时间（秒），默认1小时
        :return: dict 包含普通URL和签名URL
        """
        # 初始化 COS 配置
        secretId = CommonUtils.get_env('COS_PRIVATE_SecretId', TencentUtils.COS_CONFIG['SecretId'])
        secretKey = CommonUtils.get_env('COS_PRIVATE_SecretKey', TencentUtils.COS_CONFIG['SecretKey'])
        client = TencentUtils.getCosClient(secretId, secretKey)

        try:
            # 生成签名URL
            signed_url = client.get_presigned_url(
                Method='GET',
                Bucket=TencentUtils.COS_CONFIG['Bucket'],
                Key=cos_file_path,
                Expired=expire_seconds
            )

            # 普通访问URL
            normal_url = TencentUtils.COS_CONFIG['BaseUrl'] + cos_file_path

            return {
                'normal_url': normal_url,
                'signed_url': signed_url,
                'cos_path': cos_file_path,
                'expire_seconds': expire_seconds
            }

        except Exception as e:
            raise e
