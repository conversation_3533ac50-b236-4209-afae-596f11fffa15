{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-03-12T06:55:14.516746Z", "start_time": "2025-03-12T06:55:08.413561Z"}}, "source": ["import pandasai as pai\n", "\n", "pai.api_key.set(\"PAI-17879ab1-92ba-491a-9abc-9f5090e31446\")"], "outputs": [], "execution_count": 1}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-12T06:55:16.583086Z", "start_time": "2025-03-12T06:55:16.524746Z"}}, "cell_type": "code", "source": ["df = pai.read_csv(\"crf.csv\")\n", "df"], "id": "c0e3e2969d713e54", "outputs": [{"data": {"text/plain": ["PandaAI DataFrame(name='table_crf')\n", "     患者编号 患者姓名缩写      病历编号 研究者姓名      研究单位名称 病理学诊断为乳腺癌  \\\n", "0       1     陈锋    459245    徐菲  中山大学肿瘤防治中心         是   \n", "1       2    余万秋    425116    徐菲  中山大学肿瘤防治中心         是   \n", "2       3    孙艳敏    468922    徐菲  中山大学肿瘤防治中心         是   \n", "3       4    邓小萍    218011    徐菲  中山大学肿瘤防治中心         是   \n", "4       5    黄文若    474760    徐菲  中山大学肿瘤防治中心         是   \n", "..    ...    ...       ...   ...         ...       ...   \n", "225   226    陈云英  M3515220   NaN         NaN       NaN   \n", "226   227    林彦华  M3173945   NaN         NaN       NaN   \n", "227   228    李丽坤  M3577864   NaN         NaN       NaN   \n", "228   229     任燕    678830   NaN         NaN       NaN   \n", "229   230    杨兆明  M3069326   NaN         NaN       NaN   \n", "\n", "    患者曾接受PIK3CA检测，且有明确的检测结果 临床或病理资料不全 是否入组        审核日期  ...  \\\n", "0                         是         否    是  2024/11/12  ...   \n", "1                         是         否    是  2024/11/12  ...   \n", "2                         是         否    是  2024/11/12  ...   \n", "3                         是         否    是  2024/11/12  ...   \n", "4                         是         否    是  2024/11/13  ...   \n", "..                      ...       ...  ...         ...  ...   \n", "225                     NaN       NaN  NaN         NaN  ...   \n", "226                     NaN       NaN  NaN         NaN  ...   \n", "227                     NaN       NaN  NaN         NaN  ...   \n", "228                     NaN       NaN  NaN         NaN  ...   \n", "229                     NaN       NaN  NaN         NaN  ...   \n", "\n", "    是否存在不确定类型（需要研究者核对） Unnamed: 475 是否有放疗史  是否有其他治疗史 开始日期 结束日期 其他治疗方案具体填写  \\\n", "0                    2          NaN      是         否  NaN  NaN        NaN   \n", "1                    1          NaN      是         否  NaN  NaN        NaN   \n", "2                    2          NaN      是         否  NaN  NaN        NaN   \n", "3                    2          NaN      否         否  NaN  NaN        NaN   \n", "4                    2          NaN      否         否  NaN  NaN        NaN   \n", "..                 ...          ...    ...       ...  ...  ...        ...   \n", "225                NaN          NaN    NaN       NaN  NaN  NaN        NaN   \n", "226                NaN          NaN    NaN       NaN  NaN  NaN        NaN   \n", "227                NaN          NaN    NaN       NaN  NaN  NaN        NaN   \n", "228                NaN          NaN    NaN       NaN  NaN  NaN        NaN   \n", "229                NaN          NaN    NaN       NaN  NaN  NaN        NaN   \n", "\n", "    是否死亡 死亡日期      末次随访日期  \n", "0      否  NaN   2024/11/7  \n", "1     uk  NaN   2021/9/10  \n", "2      否  NaN   2023/1/28  \n", "3      否  NaN  2024/10/23  \n", "4     uk  NaN   2021/4/13  \n", "..   ...  ...         ...  \n", "225  NaN  NaN         NaN  \n", "226  NaN  NaN         NaN  \n", "227  NaN  NaN         NaN  \n", "228  NaN  NaN         NaN  \n", "229  NaN  NaN         NaN  \n", "\n", "[230 rows x 484 columns]"], "text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>患者编号</th>\n", "      <th>患者姓名缩写</th>\n", "      <th>病历编号</th>\n", "      <th>研究者姓名</th>\n", "      <th>研究单位名称</th>\n", "      <th>病理学诊断为乳腺癌</th>\n", "      <th>患者曾接受PIK3CA检测，且有明确的检测结果</th>\n", "      <th>临床或病理资料不全</th>\n", "      <th>是否入组</th>\n", "      <th>审核日期</th>\n", "      <th>...</th>\n", "      <th>是否存在不确定类型（需要研究者核对）</th>\n", "      <th>Unnamed: 475</th>\n", "      <th>是否有放疗史</th>\n", "      <th>是否有其他治疗史</th>\n", "      <th>开始日期</th>\n", "      <th>结束日期</th>\n", "      <th>其他治疗方案具体填写</th>\n", "      <th>是否死亡</th>\n", "      <th>死亡日期</th>\n", "      <th>末次随访日期</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>陈锋</td>\n", "      <td>459245</td>\n", "      <td>徐菲</td>\n", "      <td>中山大学肿瘤防治中心</td>\n", "      <td>是</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>是</td>\n", "      <td>2024/11/12</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>2024/11/7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>余万秋</td>\n", "      <td>425116</td>\n", "      <td>徐菲</td>\n", "      <td>中山大学肿瘤防治中心</td>\n", "      <td>是</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>是</td>\n", "      <td>2024/11/12</td>\n", "      <td>...</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>uk</td>\n", "      <td>NaN</td>\n", "      <td>2021/9/10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>孙艳敏</td>\n", "      <td>468922</td>\n", "      <td>徐菲</td>\n", "      <td>中山大学肿瘤防治中心</td>\n", "      <td>是</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>是</td>\n", "      <td>2024/11/12</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>2023/1/28</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>邓小萍</td>\n", "      <td>218011</td>\n", "      <td>徐菲</td>\n", "      <td>中山大学肿瘤防治中心</td>\n", "      <td>是</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>是</td>\n", "      <td>2024/11/12</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>否</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>2024/10/23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>黄文若</td>\n", "      <td>474760</td>\n", "      <td>徐菲</td>\n", "      <td>中山大学肿瘤防治中心</td>\n", "      <td>是</td>\n", "      <td>是</td>\n", "      <td>否</td>\n", "      <td>是</td>\n", "      <td>2024/11/13</td>\n", "      <td>...</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "      <td>否</td>\n", "      <td>否</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>uk</td>\n", "      <td>NaN</td>\n", "      <td>2021/4/13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>225</th>\n", "      <td>226</td>\n", "      <td>陈云英</td>\n", "      <td>M3515220</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>226</th>\n", "      <td>227</td>\n", "      <td>林彦华</td>\n", "      <td>M3173945</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>227</th>\n", "      <td>228</td>\n", "      <td>李丽坤</td>\n", "      <td>M3577864</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>228</th>\n", "      <td>229</td>\n", "      <td>任燕</td>\n", "      <td>678830</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229</th>\n", "      <td>230</td>\n", "      <td>杨兆明</td>\n", "      <td>M3069326</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>230 rows × 484 columns</p>\n", "</div>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "execution_count": 2}, {"metadata": {"ExecuteTime": {"end_time": "2025-03-12T06:56:14.204379Z", "start_time": "2025-03-12T06:56:08.107135Z"}}, "cell_type": "code", "source": ["response = df.chat(\"一共有多少条记录?\")\n", "response"], "id": "34e19a41fececf75", "outputs": [{"ename": "PandaAIApiCallError", "evalue": "Error code: 400 - {'error': {'message': \"This model's maximum context length is 8192 tokens. However, you requested 9993 tokens (7993 in the messages, 2000 in the completion). Please reduce the length of the messages or completion.\", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mPandaAIApiCallError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m response \u001b[38;5;241m=\u001b[39m df\u001b[38;5;241m.\u001b[39mchat(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m一共有多少条记录?\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m      2\u001b[0m response\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/dataframe/base.py:118\u001b[0m, in \u001b[0;36mDataFrame.chat\u001b[0;34m(self, prompt, sandbox)\u001b[0m\n\u001b[1;32m    112\u001b[0m     \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01magent\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m (\n\u001b[1;32m    113\u001b[0m         Agent,\n\u001b[1;32m    114\u001b[0m     )\n\u001b[1;32m    116\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_agent \u001b[38;5;241m=\u001b[39m Agent([\u001b[38;5;28mself\u001b[39m], sandbox\u001b[38;5;241m=\u001b[39msandbox)\n\u001b[0;32m--> 118\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_agent\u001b[38;5;241m.\u001b[39mchat(prompt)\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/agent/base.py:91\u001b[0m, in \u001b[0;36mAgent.chat\u001b[0;34m(self, query, output_type)\u001b[0m\n\u001b[1;32m     87\u001b[0m \u001b[38;5;250m\u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     88\u001b[0m \u001b[38;5;124;03mStart a new chat interaction with the assistant on Dataframe.\u001b[39;00m\n\u001b[1;32m     89\u001b[0m \u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m     90\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mstart_new_conversation()\n\u001b[0;32m---> 91\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_process_query(query, output_type)\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/agent/base.py:247\u001b[0m, in \u001b[0;36mAgent._process_query\u001b[0;34m(self, query, output_type)\u001b[0m\n\u001b[1;32m    244\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state\u001b[38;5;241m.\u001b[39massign_prompt_id()\n\u001b[1;32m    246\u001b[0m \u001b[38;5;66;03m# Generate code\u001b[39;00m\n\u001b[0;32m--> 247\u001b[0m code \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mgenerate_code(query)\n\u001b[1;32m    249\u001b[0m \u001b[38;5;66;03m# Execute code with retries\u001b[39;00m\n\u001b[1;32m    250\u001b[0m result \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mexecute_with_retries(code)\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/agent/base.py:107\u001b[0m, in \u001b[0;36mAgent.generate_code\u001b[0;34m(self, query)\u001b[0m\n\u001b[1;32m    104\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mlog(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGenerating new code...\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    105\u001b[0m prompt \u001b[38;5;241m=\u001b[39m get_chat_prompt_for_sql(\u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state)\n\u001b[0;32m--> 107\u001b[0m code \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_code_generator\u001b[38;5;241m.\u001b[39mgenerate_code(prompt)\n\u001b[1;32m    108\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_state\u001b[38;5;241m.\u001b[39mlast_prompt_used \u001b[38;5;241m=\u001b[39m prompt\n\u001b[1;32m    109\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m code\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/core/code_generation/base.py:47\u001b[0m, in \u001b[0;36mCodeGenerator.generate_code\u001b[0;34m(self, prompt)\u001b[0m\n\u001b[1;32m     44\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mlog(error_message)\n\u001b[1;32m     45\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mlog(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mStack Trace:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mstack_trace\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m---> 47\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m e\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/core/code_generation/base.py:34\u001b[0m, in \u001b[0;36mCodeGenerator.generate_code\u001b[0;34m(self, prompt)\u001b[0m\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mlog(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mUsing Prompt: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mprompt\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     33\u001b[0m \u001b[38;5;66;03m# Generate the code\u001b[39;00m\n\u001b[0;32m---> 34\u001b[0m code \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context\u001b[38;5;241m.\u001b[39mconfig\u001b[38;5;241m.\u001b[39mllm\u001b[38;5;241m.\u001b[39mgenerate_code(prompt, \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context)\n\u001b[1;32m     35\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context\u001b[38;5;241m.\u001b[39mlast_code_generated \u001b[38;5;241m=\u001b[39m code\n\u001b[1;32m     36\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_context\u001b[38;5;241m.\u001b[39mlogger\u001b[38;5;241m.\u001b[39mlog(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCode Generated:\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;132;01m{\u001b[39;00mcode\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/llm/base.py:172\u001b[0m, in \u001b[0;36mLLM.generate_code\u001b[0;34m(self, instruction, context)\u001b[0m\n\u001b[1;32m    161\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mgenerate_code\u001b[39m(\u001b[38;5;28mself\u001b[39m, instruction: BasePrompt, context: AgentState) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mstr\u001b[39m:\n\u001b[1;32m    162\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m    163\u001b[0m \u001b[38;5;124;03m    Generate the code based on the instruction and the given prompt.\u001b[39;00m\n\u001b[1;32m    164\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    170\u001b[0m \n\u001b[1;32m    171\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m--> 172\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mcall(instruction, context)\n\u001b[1;32m    173\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_extract_code(response)\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/llm/bamboo_llm/base.py:21\u001b[0m, in \u001b[0;36mBambooLLM.call\u001b[0;34m(self, instruction, _context)\u001b[0m\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcall\u001b[39m(\u001b[38;5;28mself\u001b[39m, instruction: BasePrompt, _context\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m \u001b[38;5;28mstr\u001b[39m:\n\u001b[0;32m---> 21\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_session\u001b[38;5;241m.\u001b[39mpost(\n\u001b[1;32m     22\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/query\u001b[39m\u001b[38;5;124m\"\u001b[39m, json\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mprompt\u001b[39m\u001b[38;5;124m\"\u001b[39m: instruction\u001b[38;5;241m.\u001b[39mto_string()}\n\u001b[1;32m     23\u001b[0m     )\n\u001b[1;32m     24\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m response[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124manswer\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/helpers/session.py:47\u001b[0m, in \u001b[0;36mSession.post\u001b[0;34m(self, path, **kwargs)\u001b[0m\n\u001b[1;32m     46\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mpost\u001b[39m(\u001b[38;5;28mself\u001b[39m, path\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs):\n\u001b[0;32m---> 47\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmake_request(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPOST\u001b[39m\u001b[38;5;124m\"\u001b[39m, path, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs)\n", "File \u001b[0;32m~/anaconda3/envs/vanna/lib/python3.11/site-packages/pandasai/helpers/session.py:98\u001b[0m, in \u001b[0;36mSession.make_request\u001b[0;34m(self, method, path, headers, params, data, json, timeout, **kwargs)\u001b[0m\n\u001b[1;32m     96\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m PandaAIApiCallError(data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmessage\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m     97\u001b[0m         \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdetail\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m data:\n\u001b[0;32m---> 98\u001b[0m             \u001b[38;5;28;01mraise\u001b[39;00m PandaAIApiCallError(data[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdetail\u001b[39m\u001b[38;5;124m\"\u001b[39m])\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m data\n\u001b[1;32m    102\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m requests\u001b[38;5;241m.\u001b[39mexceptions\u001b[38;5;241m.\u001b[39mRequestException \u001b[38;5;28;01mas\u001b[39;00m e:\n", "\u001b[0;31mPandaAIApiCallError\u001b[0m: Error code: 400 - {'error': {'message': \"This model's maximum context length is 8192 tokens. However, you requested 9993 tokens (7993 in the messages, 2000 in the completion). Please reduce the length of the messages or completion.\", 'type': 'invalid_request_error', 'param': 'messages', 'code': 'context_length_exceeded'}}"]}], "execution_count": 4}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}