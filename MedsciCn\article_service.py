import requests
import logging
from Utils.common_utils import CommonUtils
from Utils.common_result import CommonResult
from Utils.html_utils import HTMLUtils
from MedsciCn.medsci_url import MedsciUrl,ArticleRequest

# 配置日志记录
logging.basicConfig(level=logging.INFO)

class ArticleService:
    DEFAULT_PROJECT_ID = 1
    DEFAULT_USER_ID = 5155447
    DEFAULT_USERNAME = CommonUtils.MEDSCI_XAI
    DEFAULT_EDITOR_ID = 6809509
    DEFAULT_EDITOR = CommonUtils.MEDSCI_XAI
    APPROVAL_STATUS = [0, 1, 2]
    USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36 Edg/111.0.1661.44"

    def save_article(self, data):
        """
        主站发布资讯，参数示例
        {
            "projectId": 1,
            "projectIds": [
                1,
            ],
            "attachmentList": [],
            "fileUrl": "",
            "userId": 5155447,
            "username": "administrator",
            "title": "梅斯小智测试1",
            "authorId": 0,
            "author": "",
            "opened": 1,
            "paymentType": 1,
            "paymentAmount": 0,
            "originalUrl": "",
            "linkOutUrl": "",
            "pcVisible": 1,
            "appVisible": 1,
            "recommend": 0,
            "recommendEndTime": "",
            "sticky": 0,
            "stickyEndTime": "",
            "diamonds": 0,
            "diamondsEndTime": "",
            "articleFrom": "网络",
            "journalId": 0,
            "content": "&lt;p&gt;&amp;lt;p&amp;gt;&amp;lt;img src=&quot;https://img.medsci.cn/20250210/1739202903448_1976717.png&quot;&amp;gt;&amp;lt;/p&amp;gt;&lt;/p&gt;",
            "copyright": "转发",
            "summary": "一项最新研究表明，孕期咽部气流受限可能是导致子痫前期、妊娠期高血压疾病（HDP）以及胎儿出生体重下降的重要风险因素。该研究基于“初产妇妊娠结局监测研究（nuMoM2b）”的前瞻性数据，采用新型的客观测",
            "editorId": 6809509,
            "editor": "柏佳欢",
            "publishedTime": "2025-02-11 14:57:10",
            "waterMark": 0,
            "formatted": 0,
            "cover": "",
            "expandRead": "",
            "articleKeywordId": 0,
            "articleKeyword": "",
            "articleKeywordNum": 6,
            "guiderKeywordId": 0,
            "guiderKeyword": "",
            "guiderKeywordNum": 6,
            "approvalStatus": 0,
            "tagList": [],
            "categoryList": [],
            "creationTypeList": [],
            "labelList": [],
            "drainageInfoDtoList": [],
            "hospitalTagList": [],
            "professorTagList": [],
            "guideDownload": 1,
            "creationType": 0,
            "multiCover": [],
            "labels": []
            }
        """

        url = MedsciUrl.get_save_article_url()
        response = self.request_post(url, data)
        logging.info(f"==1==:{url}")
        # logging.info(f"==2==:{data}")
        # logging.info(f"==3==:{response.json()}")

        # {
        #     "status": 200,
        #     "message": "success",
        #     "data": null
        # }
        # 根据上方的返回值，判断是否请求成功，并返回相应的结果。
        return response.json()
    
    def save_article_simple(self, article: ArticleRequest):
        # 提取参数并设置默认值
        title = article.title
        content = article.content
        summary = article.summary
        
        # 初始化返回结果
        res = CommonResult.success()

        # 输入验证
        if not title or not content or not summary:
            return CommonResult.error(message="标题、摘要、内容不能为空")
        
        # 设置认值
        publishedTime = CommonUtils.get_time_format_or_default(article.publishedTime)
        author = article.author if article.author else self.DEFAULT_EDITOR
        copyright = article.copyright if article.copyright else "转载"
        articleFrom = article.articleFrom if article.articleFrom else "网络"
        editor = self.DEFAULT_EDITOR
        editorId = self.DEFAULT_EDITOR_ID
        if article.editor:
            x = self.get_usr_by_name(article.editor)
            if x.data and len(x.data) > 0 and x.data[0].get('userName') == article.editor:
                editorId = x.data[0].get('userId')
                editor = article.editor

        tagList = []
        if article.tagListStr:
            for tag in article.tagListStr.split(","):
                tag = tag.strip()
                if tag == "":
                    continue
                x = self.get_tag_by_name(tag)
                if x.data :
                    for x_data in x.data:
                        if x_data.get('tagName') == tag:
                            tagList.append({
                                "tagId": x_data.get('id'),
                                "tagName": tag,
                            })
                            break
        
        hcoList = []
        hospitalTagList = []
        if article.hospitalTagListStr:
            for tag in article.hospitalTagListStr.split(","):
                tag = tag.strip()
                if tag == "":
                    continue
                x = self.get_hco_by_name(tag)
                if x.data :
                    for x_data in x.data:
                        if x_data.get('name') == tag:
                            hospitalTagList.append({
                                "tagId": x_data.get('hospitalId'),
                                "tagName": tag,
                            })
                            hcoList.append(tag)
                            break

        professorTagList = []
        if article.professorTagListStr:
            for tag in article.professorTagListStr.split(","):
                tag = tag.strip()
                if tag == "":
                    continue
                # tag删除左右空格
                x = self.get_hcp_by_name(tag)
                if x.data :
                    for x_data in x.data:
                        if x_data.get('hcpName') == tag and x_data.get('hcoName') in hcoList:
                            professorTagList.append({
                                "tagId": x_data.get('mobile'),
                                "tagName": tag,
                            })
                            break
        
        journalId = 0
        if article.journalIdStr:
            for tag in article.journalIdStr.split(","):
                tag = tag.strip()
                if tag == "":
                    continue
                x = self.get_sci_by_name(tag)
                if x.data :
                    for journal in x.data:
                        if journal.get('fullname') == article.journalIdStr:
                            journalId = journal.get('id')
                            break

        categoryList = []
        if article.categoryListStr:
            cateList = article.categoryListStr.split(",")
            cateList = [category.strip() for category in cateList]  # 清除每个元素前后的空格
            x = self.get_cate_by_name()
            if x.data :
                for x_data in x.data:
                    for child in x_data.get('children'):
                        if child.get('titleCn') in cateList:
                            categoryList.append({
                                "categoryId": child.get('categoryId'),
                                "categoryName": child.get('titleCn'),
                                "tenant": child.get('tenant'),
                            })

        approvalStatus = 0
        try:
            article.approvalStatus = article.approvalStatus if article.approvalStatus else "0"
            approvalStatus = int(article.approvalStatus)
            # 0.待审批 1.审批通过 2.草稿
            if approvalStatus not in self.APPROVAL_STATUS:
                approvalStatus = 0
        except ValueError as e:
            print(f"Error: {e}")

        # 构建参数字典
        param = {
            "projectId": self.DEFAULT_PROJECT_ID,
            "projectIds": [self.DEFAULT_PROJECT_ID],
            "attachmentList": [],
            "fileUrl": "",
            "userId": self.DEFAULT_USER_ID,
            "username": self.DEFAULT_USERNAME,
            "title": title,
            "authorId": 0,
            "author": author,
            "opened": 1 if article.opened == "1" else 0,
            "paymentType": 1,
            "paymentAmount": 0,
            "originalUrl": "",
            "linkOutUrl": article.linkOutUrl if article.linkOutUrl else "",
            "pcVisible": 0 if article.opened == "0" else 1,
            "appVisible": 0 if article.appVisible == "0" else 1,
            "recommend": 1 if article.recommend == "1" else 0,
            "recommendEndTime": CommonUtils.get_time_format(article.recommendEndTime) if article.recommendEndTime else "",
            "sticky": 1 if article.sticky == "1" else 0,
            "stickyEndTime": CommonUtils.get_time_format(article.stickyEndTime) if article.stickyEndTime else "",
            "diamonds": 0,
            "diamondsEndTime": "",
            "articleFrom": articleFrom,
            "journalId": journalId,
            "content": content,
            "copyright": copyright,
            "summary": summary,
            "editorId": editorId,
            "editor": editor,
            "publishedTime": publishedTime,
            "waterMark": 0,
            "formatted": 0,
            "cover": "",
            "expandRead": "",
            "articleKeywordId": 0,
            "articleKeyword": "",
            "articleKeywordNum": 0,
            "guiderKeywordId": 0,
            "guiderKeyword": "",
            "guiderKeywordNum": 0,
            "approvalStatus": approvalStatus,
            "tagList": tagList,
            "categoryList": categoryList,
            "creationTypeList": [],
            "labelList": [],
            "drainageInfoDtoList": [],
            "hospitalTagList": hospitalTagList,
            "professorTagList": professorTagList,
            "guideDownload": 1,
            "creationType": 0,
            "multiCover": [],
            "labels": []
        }

        try:
            jsonRes = self.save_article(data=param)
            return CommonResult(status=jsonRes.get('status'), message=jsonRes.get('message'), data=jsonRes.get('data'))
        except Exception as e:
            res = CommonResult.error(message= f"保存文章失败: {str(e)}")
            return res

    def get_article_by_id(self, id: str):
        """
        根据文章ID获取文章信息
        """
        res = CommonResult.success(data=None)
        url = MedsciUrl.get_article_url()
        param = {
            "projectId": 1,
            "id": id
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return CommonResult.success(data=response.json().get('data'))
        except Exception as e:
            logging.error(f"get_article_by_id error: {str(e)}")
            return res 

    def get_hco_by_name(self, name: str, page_size: int = 1):
        """
        根据医院名称获取医院信息
        """
        res = CommonResult.success(data=[])
        if not name:
            return res

        url = MedsciUrl.get_hco_by_name_url()
        param = {
            "projectId": 1,
            "projectIds": [],
            "name": name,
            "pageIndex": 1,
            "pageSize": page_size,
            "userIdNotNull": False
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return CommonResult.success(data=response.json().get('data'))
        except Exception as e:
            logging.error(f"get_hco_by_name error: {str(e)}")
            return res 

    def get_hcp_by_name(self, name: str, page_size: int = 1):
        """
        根据专家名称获取专家信息
        """
        res = CommonResult.success(data=[])
        if not name:
            return res
        
        url = MedsciUrl.get_hcp_by_name_url()
        param = {
            "projectId": 1,
            "projectIds": [],
            "hcpName": name,
            "pageIndex": 1,
            "pageSize": page_size
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return CommonResult.success(data=response.json().get('data'))
        except Exception as e:
            logging.error(f"get_hcp_by_name error: {str(e)}")
            return res 

    def get_cate_by_name(self, name: str = 'article'):
        """
        根据栏目名称获取栏目信息
        """
        res = CommonResult.success(data=[])
        if not name:
            return res
        
        url = MedsciUrl.get_cate_by_name_url()
        param = {
            "projectId": 1,
            "projectIds": [],
            "module": name
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return CommonResult.success(data=response.json().get('data'))
        except Exception as e:
            logging.error(f"get_cate_by_name error: {str(e)}")
            return res 

    def get_tag_by_name(self, name: str):
        """
        根据关键词名称获取关键词信息
        """
        res = CommonResult.success(data=[])
        if not name:
            return res
        
        url = MedsciUrl.get_tag_by_name_url()
        param = {
            "projectId": 1,
            "projectIds": [],
            "tagName": name
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return CommonResult.success(data=response.json().get('data'))
        except Exception as e:
            logging.error(f"get_tag_by_name error: {str(e)}")
            return res 

    def get_usr_by_name(self, name: str, page_size: int = 150, projectIds: list = None):
        """
        根据用户名称获取用户信息
        """
        res = CommonResult.success(data=[])
        if not name:
            return res
        
        url = MedsciUrl.get_usr_by_name_url()
        param = {
            "projectId": 1,
            "pageIndex": 1,
            "pageSize": page_size,
            "roleId": 53 if MedsciUrl.get_is_prod() else 203,
            "name": name
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return CommonResult.success(data=response.json().get('data'))
        except Exception as e:
            logging.error(f"get_usr_by_name error: {str(e)}")
            return res 

    def get_sci_by_name(self, name: str):
        """
        根据期刊名称获取期刊信息
        """
        res = CommonResult.success(data=[])
        if not name:
            return res
        
        url = MedsciUrl.get_sci_by_name_url()
        param = {
            "projectId": 1,
            "projectIds": [],
            "title": name
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return CommonResult.success(data=response.json().get('data'))
        except Exception as e:
            logging.error(f"get_sci_by_name error: {str(e)}")
            return res 
    
    def get_claim_by_name(self, type:int=1, categoryIds: list = [], parentIds: list = []):
        """
        根据栏目名称获取亚专业 疾病信息
        """
        res = CommonResult.success(data=[])
        
        url = MedsciUrl.get_cliam_by_name_url()
        param = {
            "projectId": 1,
            "projectIds": [],
            "type": type,
            "categoryIds": categoryIds,
            "parentIds": parentIds
        }
        try:
            response = self.request_post(url, param)
            if response.status_code != 200:
                return res
            if response.json().get("status") != 200:
                return res
        
            return response.json()
        except Exception as e:
            logging.error(f"get_claim_by_name error: {str(e)}")
            return res 
        
    def request_post(self, url: str, param: dict):
        headers = {
            "Content-Type": "application/json",
            "User-Agent": self.USER_AGENT,
        }
        response = requests.post(url, headers=headers, json=param)
        return response

    def html_2_markdown(self, html: str):
        return HTMLUtils.html_2_markdown(html)

    def escaped_html_2_txt(self, content: str):
        return HTMLUtils.escaped_html_2_txt(content)

article_service = ArticleService()