from fastapi import UploadFile
import pymupdf
import tempfile
import os
import shutil
from Utils.tencent_utils import TencentUtils
from Utils.common_utils import CommonUtils
import requests


class FileUtils:
    TEMP_DIR = "Temp"
    MAX_SIZE = 1024 * 1024 * 10 

    @staticmethod
    def get_temp_dir() -> str:
        """
        获取临时目录路径。
        """
        return os.path.join(os.getcwd(), FileUtils.TEMP_DIR)
    
    @staticmethod
    def get_file_name(file_path:str) -> str:
        """
        获取文件名。
        """
        return os.path.splitext(os.path.basename(file_path))[0]

    @staticmethod
    def upload_file(file: UploadFile, file_name:str=None) -> dict:
        """
        上传文件到服务器并返回文件路径。
        :param file: 文件
        :param file_name: 文件名，带后缀，默认为None
        """
        # 判断文件大小
        if file.size > FileUtils.MAX_SIZE:
            raise '文件大小错误，请上传小于10MB的文件'
        
        file_name = file_name if file_name else file.filename
                     
        file_path = FileUtils.get_temp_dir() + '/' + file_name

        # 保存文件到本地
        try:
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
        except Exception as e:
            raise e
        finally:
            file.file.close()

        return {'file_path':file_path, 'file_name':file_name}

    @staticmethod
    def upload_pdf(file: UploadFile) -> dict:
        """
        上传PDF文件到服务器并返回文件路径。
        :param file: PDF文件
        """
        # 判断文件类型
        if file.content_type != 'application/pdf':
            raise '文件类型错误，请上传PDF文件'

        return FileUtils.upload_file(file, file.filename)

    @staticmethod
    def get_imgs_from_pdf(pdf_path: str, start_index:int=0, is_temp:bool=True) -> list:
        """
        从PDF中提取图片并返回图片的URL列表。
        
        :param pdf_path: PDF文件路径
        :param start_index: 开始提取图片的页码索引，默认为0
        :param delete: 是否在提取图片后删除临时文件，默认为True
        :return: 图片的URL列表
        """
        urls = []
        try:
            doc = pymupdf.open(pdf_path)
            dir = FileUtils.get_temp_dir()
            file_name = CommonUtils.get_md5(FileUtils.get_file_name(pdf_path))

            if not doc or len(doc) == 0:
                print("No pages found in the PDF.")
                return urls
            start_index = start_index if start_index >= 0 else 0
            if start_index > len(doc):
                print("Start index out of range.")
                return urls
            
            for page_index in range(len(doc)): # iterate over pdf pages
                if page_index < start_index:
                    continue
                page = doc[page_index] # get the page
                image_list = page.get_images()

                # print the number of images found on the page
                if image_list:
                    print(f"Found {len(image_list)} images on page {page_index}")
                else:
                    print("No images found on page", page_index)
                    continue

                # 创建临时图片文件
                for image_index, img in enumerate(image_list, start=1): # enumerate the image list
                    xref = img[0] # get the XREF of the image
                    img_obj = doc.extract_image(xref)
                    suffix = img_obj['ext']

                    pix = pymupdf.Pixmap(doc, xref) # create a Pixmap
                    if pix.n - pix.alpha > 3: # CMYK: convert to RGB first
                        pix = pymupdf.Pixmap(pymupdf.csRGB, pix)

                    uuid = CommonUtils.get_time_format_or_default(format=CommonUtils.Ymd_HMSF)
                    img_name = f"{file_name}/page_{page_index + 1}-image_{image_index}-uuid_{uuid}.{suffix}"

                    # 将图片保存到临时文件中
                    with tempfile.NamedTemporaryFile(dir=dir, delete=True) as temp_file:
                        temp_file.write(pix.tobytes())

                        url = TencentUtils.upload_file_to_cos(temp_file.name, TencentUtils.get_cos_path(is_temp)+img_name)
                        urls.append(url)
                    # pix.save(img_name) # save the image as png
                    pix = None
            
        except Exception as e:
            raise e
        
        return urls
        
    @staticmethod
    def pdf_to_images(pdf_path: str, start_index:int=0, is_temp: bool=False, zoom: float = 2.0) -> list:
        """
        将PDF文件转换为图片
        :param pdf_path: PDF文件路径
        :param output_dir: 输出目录，默认为 TEMP_DIR
        :param zoom: 缩放比例，默认2.0
        :return: 生成的图片路径列表
        """
        urls = []

        try:
            doc = pymupdf.open(pdf_path)
            if not doc or len(doc) == 0:
                    print("No pages found in the PDF.")
                    return urls
            start_index = start_index if start_index >= 0 else 0
            if start_index > len(doc):
                print("Start index out of range.")
                return urls
            
            dir = FileUtils.get_temp_dir()
            file_name = CommonUtils.get_md5(FileUtils.get_file_name(pdf_path))
            mat = pymupdf.Matrix(zoom, zoom)  # 缩放系数
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                pix = page.get_pixmap(matrix=mat)
                
                uuid = CommonUtils.get_time_format_or_default(format=CommonUtils.Ymd_HMSF)
                img_name = f"{file_name}/page_{page_num + 1}-uuid_{uuid}.png"

                # 将图片保存到临时文件中
                with tempfile.NamedTemporaryFile(dir=dir, delete=True) as temp_file:
                    temp_file.write(pix.tobytes())

                    url = TencentUtils.upload_file_to_cos(temp_file.name, TencentUtils.get_cos_path(is_temp)+img_name)
                    urls.append(url)
                # pix.save(img_name) # save the image as png
                pix = NotImplementedError
                
        except Exception as e:
            raise e
        finally:
            doc.close()
        
        return urls 

    @staticmethod
    def upload_ppt(file: UploadFile) -> dict:
        """
        上传PDF文件到服务器并返回文件路径。
        :param file: PPT文件
        """
        # 判断文件类型
        if file.content_type not in ['application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation']:
            raise ValueError('文件类型错误，请上传PPT文件')

        file_name = CommonUtils.get_time_format_or_default(format=CommonUtils.Ymd_HMS) + '_' + file.filename

        return FileUtils.upload_file(file, file_name)   

    @staticmethod
    def pptx_to_pdf(ppt_path, is_temp: bool=True)->str:
        """
        将PPT文件转换为PDF文件
        :param ppt_path: PPT文件路径
        :return: PDF url
        """
        url = ''

        with open(ppt_path, "rb") as file:
            # 构造表单数据
            files = {
                "file": (ppt_path, file)  # 文件字段，指定文件名和文件对象
            }
            data = {
                "format": "pdf"  # 指定输出格式
            }

            # 发送 POST 请求
            response = requests.post(CommonUtils.get_env('LibreOffice_HOST'), files=files, data=data)

        # 检查响应状态码
        if response.status_code == 200:
            # 获取文件名
            file_name = CommonUtils.get_md5(FileUtils.get_file_name(ppt_path))+'.pdf'
            dir = FileUtils.get_temp_dir()

            with tempfile.NamedTemporaryFile(dir=dir, delete=True) as temp_file:
                temp_file.write(response.content)
                url = TencentUtils.upload_file_to_cos(temp_file.name, TencentUtils.get_cos_path(is_temp)+file_name)
        else:
            print(f"文件转换失败，状态码: {response.status_code}, 错误信息: {response.text}")

        return url
