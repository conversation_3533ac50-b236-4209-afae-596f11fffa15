from fastapi import UploadFile
from Utils.file_utils import FileUtils
from Utils.tencent_utils import TencentUtils
from Utils.common_utils import CommonUtils
import os

class FileService:
    
    def save_pdf_imgs(self, file: UploadFile, is_temp:bool=True, startIndex: int=1) -> list:
        """
        保存pdf中的图片
        :param file:pdf文件
        :return: 图片url列表
        """
        delete = True
        startIndex = startIndex - 1
        if startIndex < 0:
            startIndex = 0

        file = FileUtils.upload_pdf(file)
        urls = FileUtils.get_imgs_from_pdf(pdf_path=file['file_path'], start_index=startIndex, is_temp=is_temp)
        
        if delete:
            # 删除临时文件
            os.remove(file['file_path'])
        
        return urls
    
    def upload_to_cos(self, file: UploadFile, is_temp:bool=True, file_name:str=None) -> dict:
        """
        保存pdf文件
        :param file:pdf文件
        :param temp:是否临时文件，7天有效
        :param file_name:文件名
        :return: 文件信息
        """
        file_name = CommonUtils.get_time_format_or_default(format=CommonUtils.Ymd_HMS) + '_' + (file_name if file_name else file.filename)
        
        file = FileUtils.upload_file(file, file_name)

        url = TencentUtils.upload_file_to_cos(file['file_path'], TencentUtils.get_cos_path(is_temp)+file_name)
        # 删除临时文件
        os.remove(file['file_path'])
        return {'url': url}
    
    def save_pdf_to_imgs(self, file: UploadFile, startIndex: int=1, is_temp: bool=False) -> list:
        """
        保存pdf中的图片
        :param file:pdf文件
        :return: 图片url列表
        """
        delete = True
        startIndex = startIndex - 1
        if startIndex < 0:
            startIndex = 0

        file = FileUtils.upload_pdf(file)
        urls = FileUtils.pdf_to_images(pdf_path=file['file_path'], start_index=startIndex, is_temp=is_temp, zoom=2.0)
        
        if delete:
            # 删除临时文件
            os.remove(file['file_path'])
        
        return urls
    
    def ppt_to_pdf(self, file: UploadFile, is_temp: bool=True) -> dict:
        """
        ppt转pdf
        :param file:ppt文件
        :return: pdf文件url
        """
        file = FileUtils.upload_file(file)
        url = FileUtils.pptx_to_pdf(file['file_path'], is_temp)

        os.remove(file['file_path'])

        return {'url': url}

    def upload_to_cos_private(self, file: UploadFile, expire_seconds: int = 3600) -> dict:
        """
        上传文件到COS私有桶并获取签名访问地址
        :param file: 上传的文件
        :param expire_seconds: 签名URL过期时间（秒），默认1小时
        :return: 文件信息，包含普通URL和签名URL
        """
        file_name = CommonUtils.get_time_format_or_default(format=CommonUtils.Ymd_HMS) + '_' + file.filename

        # 上传文件到本地临时目录
        uploaded_file = FileUtils.upload_file(file, file_name)

        # 上传到COS并获取签名URL
        result = TencentUtils.upload_file_to_cos_with_signed_url(
            uploaded_file['file_path'],
            TencentUtils.get_cos_path() + file_name,
            expire_seconds
        )

        # 删除本地临时文件
        os.remove(uploaded_file['file_path'])

        return {
            'normal_url': result['normal_url'],
            'signed_url': result['signed_url'],
            'cos_path': result['cos_path'],
            'expire_seconds': result['expire_seconds'],
            'file_name': file_name,
            'file_size': uploaded_file['file_size'],
        }



file_service = FileService()