from pydantic import BaseModel, Field
from typing import Optional
from Utils.common_utils import CommonUtils

class MedsciUrl:
    # 定义常量
    DOMAIN_DEV = "https://medsci-gateway.medon.com.cn"
    DOMAIN_PROD = "https://apigateway.medsci.cn"
    SAVE_ARTICLE_URL = "/api/mg/information/article/saveArticle"
    HCO_BY_NAME_URL = "/api/mg/paas-mgr/medsciCommonHospital/getEsMedsciCommonHospitals"
    HCP_BY_NAME_URL = "/api/mg/paas-mgr/medsciBi/getBiProfessorInfo"
    USR_BY_NAME_URL = "/api/mg/paas-mgr/medsciUserRole/getMedsciUserRolePage"
    TAG_BY_NAME_URL = "/api/mg/tag/tag/search"
    SCI_BY_NAME_URL = "/api/mg/information/toolImpactFactor/search"
    CATE_BY_NAME_URL = "/api/mg/tag/medsciProjectCategory/getProjectModuleAndCategoryList"
    CLAIM_BY_NAME_URL = "/api/mg/tag/tagLabel/getChildClaim"
    GET_ARITCLE_URL ="/api/mg/information/article/getArticleById"

    @staticmethod
    def get_is_prod() -> bool:
        """
        获取是否生产环境
        """
        env = CommonUtils.get_env('CURRENT_ACTIVE_ENV', 'dev')
        return env == 'prod'

    @staticmethod
    def get_domain() -> str:
        """
        获取当前环境域名
        """
        return MedsciUrl.DOMAIN_PROD if MedsciUrl.get_is_prod() else MedsciUrl.DOMAIN_DEV

    @staticmethod
    def get_save_article_url() -> str:
        """
        获取保存文章的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.SAVE_ARTICLE_URL
    
    @staticmethod
    def get_hco_by_name_url() -> str:
        """
        获取医院列表的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.HCO_BY_NAME_URL
    
    @staticmethod
    def get_hcp_by_name_url() -> str:
        """
        获取专家列表的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.HCP_BY_NAME_URL
    
    @staticmethod
    def get_usr_by_name_url() -> str:
        """
        获取用户列表的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.USR_BY_NAME_URL
    
    @staticmethod
    def get_tag_by_name_url() -> str:
        """
        获取关键词列表的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.TAG_BY_NAME_URL
    
    @staticmethod
    def get_sci_by_name_url() -> str:
        """
        获取期刊列表的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.SCI_BY_NAME_URL
    
    @staticmethod
    def get_cate_by_name_url() -> str:
        """
        获取栏目列表的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.CATE_BY_NAME_URL
    
    @staticmethod
    def get_cliam_by_name_url() -> str:
        """
        获取子栏目列表的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.CLAIM_BY_NAME_URL

    @staticmethod
    def get_article_url() -> str:
        """
        获取文章详情的url
        """
        return MedsciUrl.get_domain() + MedsciUrl.GET_ARITCLE_URL
    



class BaseResponse(BaseModel):
    status: int = Field(200, description="响应码")
    message: str = Field("操作成功", description="响应信息")
    data: dict|list = Field(None, description="响应数据")

    @staticmethod
    def success(data: dict|list = None, message: str = "操作成功") -> "BaseResponse":
        return BaseResponse(status=200, message=message, data=data)
    
    @staticmethod
    def error(message: str = "操作失败", data: dict|list = None) -> "BaseResponse":
        return BaseResponse(status=500, message=message, data=data)
    

class ArticleRequest(BaseModel):
    title: str = Field(..., description="文章标题,必填")
    content: str = Field(..., description="文章内容,必填")
    summary: str = Field(..., description="文章摘要,必填")
    approvalStatus: Optional[str] = Field("0", description="审批状态，0: 待审批, 1: 已审批, 2: 已拒绝")
    author: Optional[str] = Field(CommonUtils.MEDSCI_XAI, description="作者") 
    publishedTime: Optional[str] = Field(None, description="发布时间，格式为 YYYY-MM-DD HH:MM:SS")
    editor: Optional[str] = Field(CommonUtils.MEDSCI_XAI, description="编辑者") # editorId
    copyright: Optional[str] = Field("转发", description="版权信息")
    articleFrom: Optional[str] = Field("网络", description="来源")
    linkOutUrl: Optional[str] = Field("", description="外链地址")
    opened: Optional[str] = Field("1", description="是否公开，1: 公开, 0: 不公开")
    recommend: Optional[str] = Field("0", description="是否推荐，1: 推荐, 0: 不推荐")
    recommendEndTime: Optional[str] = Field("", description="推荐结束时间，格式为 YYYY-MM-DD HH:MM:SS")
    sticky: Optional[str] = Field("0", description="是否置顶，1: 置顶, 0: 不置顶")
    stickyEndTime: Optional[str] = Field("", description="置顶结束时间，格式为 YYYY-MM-DD")
    pcVisible: Optional[str] = Field("1", description="PC端是否可见，1: 可见, 0: 不可见")
    appVisible: Optional[str] = Field("1", description="移动端是否可见，1: 可见, 0: 不可见")
    journalIdStr: Optional[str] = Field("", description="所属期刊") # journalId
    tagListStr: Optional[str] = Field("", description="标签集合") # tagList
    categoryListStr: Optional[str] = Field("", description="专题分类集合") # categoryList
    labelListStr: Optional[str] = Field("", description="标签集合") # labelList
    labelsStr: Optional[str] = Field("", description="标签集合") # labels
    hospitalTagListStr: Optional[str] = Field("", description="关联医院标签集合") # hospitalTagList
    professorTagListStr: Optional[str] = Field("", description="关联专家标签集合") # professorTagList

# articleFrom creationTypeList cover multiCover originalUrl linkOutUrl 
# articleKeywordId articleKeyword articleKeywordNum guiderKeywordId guiderKeyword guiderKeywordNum
# paymentType paymentAmount waterMark formatted showAppHits showPcHits pointId videoUrl courseId
# speakerInfo courseType tenant clientIp drainageInfoDtoList attachmentList fileUrl 
# guideDownload surveyId surveyName
