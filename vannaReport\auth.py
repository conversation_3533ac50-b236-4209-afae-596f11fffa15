import requests
import streamlit as st
from streamlit_javascript import st_javascript as st_js
from loguru import logger
from dotenv import dotenv_values

def get_token():
    if (token:= st.query_params.get("token")) or (token := get_token_from_cookie()):
        return token
    return None


def get_token_from_cookie():
    cookies = str(st_js("document.cookie"))
    token_parts = [part.split('=')[1] for part in cookies.split('; ') if part.startswith('vanna_auth_token=')]
    if token_parts:
        return token_parts[0]
    else:
        return None


def verify_token(token: str=None) -> dict:
    if not token:
        token = get_token()
    logger.debug(f'{token=}')

    if not token:
        return {"valid": False, "error": "未提供访问令牌, 你的行为已被记录!"}

    # 调用权限验证API
    env_vals = dotenv_values()
    host = env_vals.get('AI_BASE_HOST')
    url = f'{host}/dev-api/admin-api/system/auth/get-permission-info'
    response = requests.get(
        url,
        headers={
            'Authorization': f'Bearer {token}',
            'User-Agent': 'VANNA REPORT AUTH APP/1.0'},
    )

    if response.status_code != 200 or response.json()['status']!=200:
        return {"valid": False, "error": "权限验证失败, 你的行为已被记录!"}

    # 解析返回的JSON数据
    user_data = response.json()['data']
    if not user_data.get('roles', []):
        return {"valid": False, "error": "未找到用户角色信息, 你的行为已被记录!"}

    # 检查是否包含tenant_admin 或 medsci_assistant
    if 'tenant_admin' not in user_data['roles'] and 'medsci_assistant' not in user_data['roles']:
        return {"valid": False, "error": "无访问权限, 你的行为已被记录!"}

    st.session_state["nickname"] = user_data["user"]["nickname"]

    # 使用st_js 写入cookie
    st_js(f"""document.cookie = "vanna_auth_token={token}; path=/;";""")
    return {"valid": True, "data": response.json()}
