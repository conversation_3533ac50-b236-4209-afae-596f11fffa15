import re
import json
import base64
import urllib
import requests
import html2text
from lxml import etree
from pydash import get
from urllib import parse
from loguru import logger
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from abc import ABC, abstractmethod
from requests import Response as RequestsResponse
from playwright.sync_api import <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON>, Page, sync_playwright, Response as PlaywrightPageResponse


# 定义抽象基类
class ExpressSpiderABC(ABC):
    """
        爬虫抽象类
    """
    def __init__(
            self,
            url,
            content_selector ='body',
            formats = 'markdown',
            request_headers = None,
            request_cookies = None,
            remove_tags = None,
            remove_attrs = None,
            remove_selectors = None,
            items_parse_rule = None
    ):
        self.url = parse.unquote(url.strip())
        self.selector = content_selector
        self.items_parse_rule = items_parse_rule.strip() if items_parse_rule else None
        self.formats = formats
        if request_headers is None:
            request_headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.159 Safari/537.36'
            }
        self.request_headers = request_headers
        self.request_cookies = request_cookies
        self.remove_tags = remove_tags
        self.remove_attrs = remove_attrs
        self.remove_selectors = remove_selectors


    """
    instance
    
    """
    instance = None

    """
        选择器
        同 formats 配合使用，限定返回的内容或截图范围
        默认值：body
    """
    selector: str

    items_parse_rule: str
    """
        返回格式
        可选项：
            - markdown： markdown 纯文本格式
            - html: html 格式
            - text: 纯文本格式
            - screenshot: base64截图 
        
        默认值：
            markdown
    """
    formats: str

    """"
        请求头
    """
    request_headers: dict

    request_cookies: dict

    """
        需要返回的mata信息
        可选项：
            - url: 当前页面url 
            - title:  当前页面标题
            - description:  当前页面简介<mate type=description>
            - language:  页面语言属性
            - keywords:  页面关键词属性
            - status_code: http status code
    """
    metadata: list

    """
        TODO 移除的dom 标签
        可选项:
            所有HTML标签

        默认值:
            ['script', 'style', 'img']
    """
    remove_tags: list

    """
        TODO 移除的dom 属性
        可选项:
            所有HTML属性
        
        默认值:
            ['style', 'class']
    """
    remove_attrs: list


    @abstractmethod
    def get_meta_url(self) -> str:
        pass

    @abstractmethod
    def get_meta_title(self) -> str:
        pass


    @abstractmethod
    def get_response_headers(self) -> dict:
        pass

    def get_metas(self):
        return {
            'url': self.get_meta_url(),
            'title': self.get_meta_title(),
            'response_headers': self.get_response_headers(),
        }

    @abstractmethod
    def get_content(self):
        pass

    def get_markdown(self, html: str):
        text_maker = html2text.HTML2Text()
        text_maker.ignore_links = True
        text_maker.ignore_images = True
        text_maker.emphasis_mark = ''
        text_maker.body_width = 0
        text = text_maker.handle(html)
        return text


playwright: Playwright|None = None
playwright_browser: Browser|None = None
playwright_context: BrowserContext|None = None

class ExpressPlaywright(ExpressSpiderABC):

    instance: Page
    page_response: PlaywrightPageResponse

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.instance = self.make_playwright_page()
        self.page_response = self.instance.goto(self.url, timeout=5000)

    def make_playwright_page(self):
        global playwright, playwright_browser, playwright_context
        if not playwright:
            logger.warning(f'playwright 未初始化，正在启动')
            playwright = sync_playwright().start()
            playwright_browser = playwright.chromium.launch(headless=True)
            playwright_context = playwright_browser.new_context()

        return playwright_context.new_page()

    def get_meta_url(self):
        return self.instance.url

    def get_meta_title(self):
        return self.instance.title()

    def get_response_headers(self) -> dict:
        return self.page_response.headers

    def get_content(self):
        dom = self.instance.locator(self.selector)
        content = {}
        if dom is not None:
            if 'text' in self.formats:
                content['text'] = dom.inner_text(timeout=300)

            if 'html' in self.formats:
                content['html'] = dom.inner_html(timeout=300)

            if 'markdown' in self.formats:
                content['markdown'] = self.get_markdown(dom.inner_html(timeout=300))

            if 'screenshot' in self.formats:
                content['screenshot'] = base64.b64encode(dom.screenshot(timeout=500)).decode('utf-8')

        return content

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.instance.close()


class ExpressRequests(ExpressSpiderABC):

    instance: RequestsResponse
    soup = None

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        self.instance = requests.get(self.url, headers=self.request_headers, timeout=8)
        self.instance.raise_for_status()

        # 读取 charset
        self.instance.encoding = get(self.instance.headers, 'content-type', 'charset=utf-8').split('charset=')[-1]
        self.soup = BeautifulSoup(self.instance.text, 'html.parser')

    def get_meta_url(self):
        return self.instance.url

    def get_meta_title(self):
        return get(self.soup, 'title.string')

    def get_meta_status_code(self):
        return self.instance.status_code

    def get_response_headers(self) -> dict:
        return dict(self.instance.headers)

    def value_fill(self, name: str, value, item_data:dict):
        if name == 'link' and re.search(r'^[0-9a-z]{10,15}$', value):
            url_parse_results = urllib.parse.urlparse(self.url)
            if item_data.get('type') == 1:
                value = f"https://live.medsci.cn/detail/{value}"
            else:
                value = f"{url_parse_results.scheme}://{url_parse_results.netloc}/study/{value}"

        elif name in ['link', 'url', 'href', 'article_url'] and not value.startswith('http'):
            value = urljoin(self.url, value)

        return value

    def parse_items_by_xpath(self, dom, items_parse_rule):
        html_tree = etree.HTML(dom.prettify())
        result = []

        def parse_value(el, value_parse_rule: str):
            xpath_parse_value = el.xpath(value_parse_rule)
            xpath_parse_value = [value.strip() for value in xpath_parse_value]
            return ''.join(xpath_parse_value)

        # 按照 items 模式解析
        if 'items' in items_parse_rule:
            items_rule = items_parse_rule.pop('items')
            items = html_tree.xpath(items_rule)
            for item_el in items:
                item_data = {}
                for name, rule in items_parse_rule.items():
                    value = parse_value(item_el, rule)
                    value = self.value_fill(name, value, item_data)
                    item_data[name] = value
                result.append(item_data)
        else:
            for name, rule in items_parse_rule.items():
                value = parse_value(html_tree, rule)
                value = self.value_fill(name, value, result)
                result.append({name: value})
        return result


    def parse_items_by_jpath(self, data, items_parse_rule):
        result = []

        # 按照 items 模式解析
        if 'items' in items_parse_rule:
            items_rule = items_parse_rule.pop('items')
            items = get(data, items_rule)
            for item_json in items:
                item_data = {}
                for name, rule in items_parse_rule.items():
                    value = get(item_json, rule)
                    value = self.value_fill(name, value, item_data)
                    item_data[name] = value

                result.append(item_data)
        else:
            for name, rule in items_parse_rule.items():
                value = get(data, rule)
                value = self.value_fill(name, value, result)
                result.append({name: value})
        return result

    def get_content(self):
        dom = self.soup.select_one(self.selector)
        content = {}
        if dom is not None:
            if 'text' in self.formats:
                content['text'] = dom.get_text()

            if 'html' in self.formats:
                content['html'] = dom.prettify()

            if 'markdown' in self.formats:
                markdown_text = self.get_markdown(dom.prettify())
                markdown_text = re.sub(r'\n{2,}', '\n', markdown_text)
                content['markdown'] = markdown_text

            if 'screenshot' in self.formats:
                content['screenshot'] = 'requests/screenshot 方法不支持'

        items_parse_rule = None
        if self.items_parse_rule:
            try:
                items_parse_rule = json.loads(self.items_parse_rule)
            except Exception as e:
                logger.error(f'items_parse_rules 格式错误，无法解析json 以忽略 items 内容解析: {e}')

        if items_parse_rule:
            if self.formats == 'items_xpath':
                content['items'] = self.parse_items_by_xpath(dom, items_parse_rule)

            elif self.formats == 'items_jpath':
                json_data = json.loads(self.soup.prettify())

                content['items'] = self.parse_items_by_jpath(json_data, items_parse_rule)

            else:
                logger.error(f'无法解析items， 未知的解析格式：{self.formats}')

        return content