import uuid
import json
import os
import time
import random
import string
from loguru import logger

class VerificationCodeManager:
    def __init__(self, file_path="verification_codes.json"):
        self.file_path = file_path
        self._ensure_file_exists()
    
    def _ensure_file_exists(self):
        """确保验证码JSON文件存在"""
        if not os.path.exists(self.file_path):
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump({"codes": []}, f, ensure_ascii=False)
    
    def _load_codes(self):
        """加载验证码数据"""
        with open(self.file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _save_codes(self, data):
        """保存验证码数据"""
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    
    def generate_code(self, expire_seconds=0, length=6, description=""):
        """生成新的验证码"""
        # 生成随机验证码
        code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
        
        # 创建验证码记录
        code_record = {
            "id": str(uuid.uuid4()),
            "code": code,
            "create_time": int(time.time()),
            "expire_time": int(time.time()) + expire_seconds if expire_seconds > 0 else 0,
            "used": False,
            "description": description
        }
        
        # 加载现有数据并添加新验证码
        data = self._load_codes()
        data["codes"].append(code_record)
        self._save_codes(data)
        
        return code_record
    
    def batch_generate_codes(self, count=500, expire_seconds=0, length=6, description="", overwrite=True):
        """批量生成验证码
        
        Args:
            count: 要生成的验证码数量
            expire_seconds: 验证码有效期（秒），0表示永不过期
            length: 验证码长度
            description: 验证码描述
            overwrite: 是否覆盖之前的验证码，True表示覆盖，False表示追加
            
        Returns:
            生成的验证码列表
        """
        current_time = int(time.time())
        new_codes = []
        
        for _ in range(count):
            # 生成随机验证码
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=length))
            
            # 创建验证码记录
            code_record = {
                "id": str(uuid.uuid4()),
                "code": code,
                "create_time": current_time,
                "expire_time": current_time + expire_seconds if expire_seconds > 0 else 0,
                "used": False,
                "description": description
            }
            new_codes.append(code_record)
        
        # 加载现有数据或创建新数据
        if overwrite:
            data = {"codes": new_codes}
        else:
            data = self._load_codes()
            data["codes"].extend(new_codes)
        
        # 保存到文件
        self._save_codes(data)
        
        return new_codes
    
    def verify_code(self, code):
        """验证验证码是否有效"""
        data = self._load_codes()
        current_time = int(time.time())
        
        for code_record in data["codes"]:
            if code_record["code"] == code and not code_record["used"]:
                # 检查是否有有效期限制
                if code_record["expire_time"] > 0 and code_record["expire_time"] <= current_time:
                    continue  # 验证码已过期
                
                # 将验证码标记为已使用
                code_record["used"] = True
                self._save_codes(data)
                return True, code_record
        
        return False, None
    
    def get_remaining_codes(self):
        """获取所有未使用且未过期的验证码"""
        data = self._load_codes()
        current_time = int(time.time())
        
        remaining_codes = []
        for code_record in data["codes"]:
            # 检查验证码是否已使用
            if code_record["used"]:
                continue
                
            # 检查是否有有效期限制
            if code_record["expire_time"] > 0 and code_record["expire_time"] <= current_time:
                continue  # 验证码已过期
            
            # 添加剩余有效时间（如果有）
            if code_record["expire_time"] > 0:
                code_record["remaining_seconds"] = code_record["expire_time"] - current_time
            
            remaining_codes.append(code_record)
        
        return remaining_codes 