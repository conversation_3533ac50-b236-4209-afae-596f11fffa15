# AI BASE

## 📄 简介
此项目为dify的api接口集合。

## 🛠️ 技术栈
列出构建项目所使用的技术、框架、库等。

- 语言：Python
- 框架：FlaskAPI
- 数据库：MySQL/PostgreSQL等


##  🔧 安装
详细说明如何安装和设置项目。

### 1. 🔄 克隆仓库：
```bash
git clone ssh://git@**********:54422/project-admin/ai-base.git
```

### 2. 🧩 创建conda运行环境:
```bash
conda create -n ai-base python=3.11
conda activate ai-base
pip install torch torchvision -i https://download.pytorch.org/whl/cpu
pip install ultralytics ultralytics-thop --no-deps -i https://pypi.tuna.tsinghua.edu.cn/simple  
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
playwright install chrome
```

### 3. 🚀 启动服务:
```bash
# 本地开发
uvicorn openapi:app --reload --host 0.0.0.0 --port 8088

# 线上部署
uvicorn openapi:app --host 0.0.0.0 --port xxxx
```

### 4. 😄💻：
```bash
# 浏览器打开
http://127.0.0.1:8000

# 看到 👋🌍 提示则表示项目启动成功
```


## ⚠️ 注意事项
- 如有生产环境keys, 请在项目根目录下创建.env文件，并添加环境变量。
- 默认环境为CPU，如有GPU环境请安装对应GPU的torch版本。

# vannaReport
cd vannaReport
streamlit run app.py --server.port 8080