import html
from bs4 import BeautifulSoup
import re
from langchain_community.document_transformers import MarkdownifyTransformer
from langchain.docstore.document import Document


class HTMLUtils:

    @staticmethod
    def html_2_markdown(html:str) -> str:
        """
        将 HTML 转换为 Markdown
        :param html: HTML 内容
        :return: Markdown 内容
        """
        html_no_comments = re.sub(r'<!--.*?-->', '', html, flags=re.DOTALL)

        md = MarkdownifyTransformer()
        docs = [Document(page_content=html_no_comments)]
        converted_docs = md.transform_documents(docs)
        markdown_content = converted_docs[0].page_content
        return markdown_content
        

    @staticmethod
    def escaped_html_2_txt(html_content: str):
        """
        从 转义后的 HTML 中提取纯文本
        :param html_content: 转义后的 HTML 内容
        :return: 纯文本
        """
        # 解码 HTML 实体
        decoded_html_content = html.unescape(html_content)

        # 使用 BeautifulSoup 解析 HTML 并提取纯文本
        soup = BeautifulSoup(decoded_html_content, 'html.parser')
        plain_text = soup.get_text()
        return plain_text.replace('\n', '').replace('\r', '')