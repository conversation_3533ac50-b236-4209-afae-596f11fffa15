import re
import pandas as pd
from pydash import get
from loguru import logger
from typing import Optional
from itertools import product
from dotenv import load_dotenv
from fastapi.responses import JSONResponse, StreamingResponse
from fastapi import FastAPI, Form, UploadFile, File, Query, HTTPException, Body
from SearchEngine.baidu import BaiduSearchEngine
from concurrent.futures import ThreadPoolExecutor
from starlette.middleware.gzip import GZipMiddleware
from crawl import ExpressPlaywright, ExpressRequests, ExpressSpiderABC
from playwright.sync_api import sync_playwright, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rowser, Playwright
from SearchEngine.amap_service import amap_service
from MedsciCn.article_service import article_service
from MedsciCn.medsci_url import ArticleRequest
from Utils.common_result import CommonResult
from SearchEngine.dcr_service import dcr_service
from Utils.moonshot_utils import MoonshotUtils
from Utils.tencent_utils import TencentUtils
from MedsciCn.file_service import file_service
from Utils.verification_code import VerificationCodeManager
from MedsciSearch.medsci_search import search_service, SearchRequest
import requests
from pycorrector import Corrector
import time
from datetime import datetime
import traceback


load_dotenv()

# uvicorn openapi:app --reload --host 0.0.0.0 --port 8088
app = FastAPI()
app.add_middleware(GZipMiddleware, minimum_size=1000)

playwright: Playwright | None = None
playwright_browser: Browser | None = None
playwright_context: BrowserContext | None = None
playwright_page: Page | None = None

# 预加载影响因子数据
df_jit_index = pd.read_pickle('sci.pkl').set_index('Name')['JIF']
df_prompts = pd.read_csv('prompts.csv')
df_category = pd.read_csv('prompt_category.csv')

# https://github.com/shibing624/pycorrector/tree/master
corrector_instance = Corrector(
    language_model_path='./model/zh_giga.no_cna_cmn.prune01244.klm', #<--- 这里改成持久化储存的模型路径
    proper_name_path='./my_custom_proper.txt',
    custom_confusion_path_or_dict='./my_custom_confusion.txt'
)


def make_playwright_page():
    global playwright, playwright_browser, playwright_context, playwright_page

    if not playwright:
        logger.warning(f'playwright 未初始化，正在启动')
        playwright = sync_playwright().start()
        playwright_browser = playwright.chromium.launch(headless=False)
        playwright_context = playwright_browser.new_context()
        playwright_page = playwright_context.new_page()

    return playwright_page



def response_format(data: dict | list = None, http_status: int = 200, msg: str = '✅'):
    return JSONResponse(
        content={
            "data": data,
            "msg": '❌' if http_status == 500 and msg == '✅' else msg,
        },
        status_code=http_status)


@app.get('/')
def hello():
    return CommonResult.success(data='👋🌍, 项目以正常运行 🚀')


@app.get('/crawl/playwright/baidu-search')
def crawl_baidu_search_by_playwright(search_keyword: str, page_size: int = 10):
    playwright_page = make_playwright_page()

    baidu = BaiduSearchEngine(playwright_page, page_size=page_size, clear_html=True)
    response = baidu.crawl(search_keyword)
        # data = {
        #     'metas': {
        #         'search_keyword': search_keyword,
        #         'url': baidu.playwright_page.url,
        #         'title': baidu.playwright_page.title(),
        #         'response_headers': response.headers,
        #     },
        #     'content': baidu.format_result()
        # }
    content = baidu.format_result()
    return response_format(content)


@app.post('/crawl')
def crawl(
        url: str = Form(),
        engine: str = Form('requests'),
        formats: str = Form('markdown'),
        content_selector: str = Form('body'),
        items_parse_rule: str = Form('')
):
    def parse_url(url_list: list):
        parse_urls = []
        for url in url_list:
            if not url.startswith('http'):
                continue

            # 找到所有符合{{}}规则的参数
            pattern = re.compile(r'(\w+)=\{\{(.*?)\}\}')
            params = pattern.findall(url)

            # 将参数模板分割为选项列表
            options = {param[0]: param[1].split('|') for param in params}

            # 移除URL模板中的参数部分，保留基础URL
            base_url, _, query_string = url.partition('?')

            # 处理不符合{{}}规则的参数
            fixed_query_string = ''
            if query_string:
                # 移除符合{{}}规则的参数
                fixed_query_string = re.sub(pattern, '', query_string)
                # 添加固定的参数
                fixed_query_string = fixed_query_string.strip('&') if fixed_query_string else ''

            if len(options) > 0:
                for values in product(*options.values()):
                    new_query_string = '&' + '&'.join([f'{key}={value}' for key, value in zip(options.keys(), values)])
                    item_url = base_url + ('?' if query_string else '') + fixed_query_string + new_query_string
                    parse_urls.append(item_url)
            else:
                parse_urls.append(base_url + ('?' if query_string else '') + fixed_query_string)

        return parse_urls

    def parse_result(express_instance: ExpressSpiderABC):
        try:
            result = {'content': express_instance.get_content()}
            if 'metas' in formats:
                result['metas'] = express_instance.get_metas()

            if 'query' in formats:
                result['query'] = {
                    'url': url,
                    'engine': engine,
                    'formats': formats,
                    'content_selector': content_selector
                }
            return result
        except Exception as e:
            logger.error(f'err, {url} => {e}')

    def results_formats(results: list[dict]):
        results_formats = {}
        for item in results:
            if item and 'metas' in item :
                results_formats.setdefault('metas', []).append(item['metas'])

            if item and 'query' in item:
                results_formats['query'] = item['query']

            if item and 'content' in item:
                results_formats.setdefault('content', {})
                for content_type in item['content']:
                    results_formats['content'].setdefault(content_type, [])
                    results_formats['content'][content_type] += item['content'][content_type]

                results_formats['content']['items_length'] = len(get(results_formats, 'content.items', []))
        return results_formats

    urls = url.strip().split('#URL#')
    urls = parse_url(urls)
    # logger.debug(url)
    if not urls:
        return response_format(msg='urls 传值异常请检查,多个URL使用英文竖线符号 | 隔开', http_status=500)

    results = []
    if engine == 'playwright':
        for url in urls:
            with ExpressPlaywright(url, formats=formats, content_selector=content_selector) as express:
                try:
                    result = parse_result(express)
                    results.append(result)
                except Exception as e:
                    logger.error(e)

    elif engine == 'requests':
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(ExpressRequests, url, formats=formats, content_selector=content_selector, items_parse_rule=items_parse_rule) for url in urls]
            for future in futures:
                try:
                    result = future.result()
                    results.append(parse_result(result))
                except Exception as e:
                    logger.error(e)

    results = results_formats(results)
    return response_format(results)


@app.post('/sci_if')
def sci_if(name:str = Form('name')):
    name_list = name.split('#')
    result_dict = df_jit_index.reindex(name_list).fillna('-1').to_dict()
    sorted_result = dict(sorted(result_dict.items(), key=lambda item: item[1], reverse=True))

    return sorted_result

@app.get("/prompt")
async def search(
        category: Optional[str] = Query(None, description="分类"),
        keyword: Optional[str] = Query(None, description="搜索内容")
):
    # 过滤数据
    filtered_data = df_prompts

    # 如果提供了 category，先从 df_category 中检索出对应的英文名称
    if category:
        if acts:= df_category[df_category['分类名称'] == category]['act'].tolist():
            filtered_data = filtered_data[filtered_data['act'].isin(acts)]

    if keyword:  # 如果 keyword 不为空，则进行过滤
        condition = (
                (filtered_data['act'].str.contains(keyword, case=False, na=False)) |
                (filtered_data['prompt'].str.contains(keyword, case=False, na=False))
        )
        filtered_data = filtered_data[condition]

    result = filtered_data.to_dict(orient='records')
    return {"results": result}

@app.post('/html2markdown')
def html2markdown(html: str = Form(...)):
    markdown_content = article_service.html_2_markdown(html)
    print(markdown_content)
    return {
        "markdown": markdown_content
    }

@app.get('/get_amap_lng_lat', summary="获取高德经纬度", description="获取高德经纬度")
def get_amap_lng_lat(address: str = Query(None, description='地址'), 
                     auth_var: str = Query(None, description='高德开放平台登录后cookie中value')):
    try :
        return CommonResult.success(data=amap_service.get_amap_lng_lat(address, auth_var))
    except Exception as e:
        return CommonResult.error(message='error: %s' % e)

@app.get('/clean_hcp', summary="dcr清洗HCP", description="dcr清洗HCP")
def clean_hcp(
    pageNo: Optional[int] = Query(1, description="页码"),
    pageSize: Optional[int] = Query(10, description="每页数"),
    startTime: Optional[str] = Query(None, description="开始时间"),
    endTime: Optional[str] = Query(None, description="截止时间")
):
    dcr_service.clean_hcp(pageNo, pageSize, startTime, endTime)
    return CommonResult.success()
   
@app.post('/save_article_simple', summary="主站发布资讯-需审", description="主站发布资讯")
def save_article_simple(article: ArticleRequest):
    try :
        return article_service.save_article_simple(article)
    except Exception as e:
        error_message = f"发生异常: {e}"
        error_message = f"参数: {article}"
        error_message += f"\n{traceback.format_exc()}"
        return CommonResult.error(message=error_message)

@app.post('/save_article_approval', summary="主站发布资讯-直发", description="主站发布资讯，不需要审核直接发布")
def save_article_approval(article: ArticleRequest):
    try :
        article.approvalStatus = '1'
        return article_service.save_article_simple(article)
    except Exception as e:
        error_message = f"发生异常: {e}"
        error_message += f"\n{traceback.format_exc()}"
        return CommonResult.error(message=error_message)

@app.get('/get_article_by_id', summary="获取主站资讯详情", description="获取主站资讯详情")
def get_article_by_id(id: str=''):
    try :
        return article_service.get_article_by_id(id)
    except Exception as e:
        return CommonResult.error(message='error: %s' % e)

@app.get('/chat_with_web_search', summary="kimi联网查询", description="kimi联网查询")
def chat_with_web_search(search_keyword: str, api_key:str='', model:str=''):
    return MoonshotUtils.chat_with_web_search(search_keyword, api_key, model)

@app.get('/chat_sse_client', summary="腾讯联网查询", description="腾讯联网查询")
def chat_sse_client(search_keyword: str, api_key:str='', sid: str='', visitor_id: str = ''):
    return TencentUtils.chat_sse_client(search_keyword, api_key, sid, visitor_id)

@app.post('/save_pdf_imgs', summary="获取PDF中的图片", description="获取PDF中的图片，并上传到cos")
def save_pdf_imgs(
    file: UploadFile = File(...),
    store: Optional[str] = Query(None, description="是否永久文件，1是，非0不是 有7天有效"),
    startIndex: Optional[str] = Query("1", description="开始提取的的页码")
):
    try :
        try :
            startIndex = int(startIndex) if startIndex else 1
        except Exception as e:
            startIndex = 1
        
        is_temp = False if store == '1' else True
        urls = file_service.save_pdf_imgs(file, is_temp, startIndex)
        return CommonResult.success(data=urls)
    except Exception as e:
        return CommonResult.error(message='error: %s' % e)

@app.post('/upload_to_cos', summary="上传文件到cos", description="上传文件到cos")
def upload_to_cos(
    file: UploadFile = File(...),
    store: Optional[str] = Query(None, description="是否永久文件，1是，非0不是 有7天有效"),
    fileName: Optional[str] = Query(None, description="自定义名称，带后缀，不传为随机串+原名称")
):
    try :
        is_temp = False if store == '1' else True
        return CommonResult.success(data=file_service.upload_to_cos(file, is_temp, fileName))
    except Exception as e:
        return CommonResult.error(message='error: %s' % e)

@app.post('/upload_to_cos_private', summary="上传文件到cos私有桶", description="上传文件到cos私有桶并获取签名访问地址")
def upload_to_cos_private(
    file: UploadFile = File(...),
    expireSeconds: Optional[int] = Query(3600, description="签名URL过期时间（秒），默认1小时")
):
    try:
        return CommonResult.success(data=file_service.upload_to_cos_private(file, expireSeconds))
    except Exception as e:
        return CommonResult.error(message='error: %s' % e)

@app.post('/escaped_html_2_txt', summary="提取文本", description="从 转义后的 HTML 中提取纯文本")
def escaped_html_2_txt(html: str = Form(...)):
    txt = article_service.escaped_html_2_txt(html)
    return CommonResult.success(data = {
        "content": txt
    })

@app.get("/pubmed/ris/{pmid}")
async def download_pubmed_ris(pmid: str):
    # 构造目标URL
    target_url = f"https://eutils.ncbi.nlm.nih.gov/entrez/eutils/efetch.fcgi?db=pubmed&id={pmid}&rettype=ris&retmode=text"

    # 返回重定向响应
    try:
        response = requests.get(target_url, stream=True)
        response.raise_for_status()
    except requests.RequestException as e:
        logger.error(e)
        raise HTTPException(status_code=400, detail=f"文件获取失败, 请稍后重试~")

        # 返回文件流响应
    return StreamingResponse(
        content=response.iter_content(chunk_size=1024),
        media_type="text/plain",
        headers={"Content-Disposition": f"attachment; filename=pubmed_{pmid}.ris"}
    )

@app.post('/save_pdf_to_imgs', summary="PDF导出为png", description="PDF导出为png，并上传到cos")
def save_pdf_to_imgs(
    file: UploadFile = File(...),
    startIndex: Optional[str] = Query("1", description="开始导出的的页码"),
    store: Optional[str] = Query(None, description="是否永久文件，1是，非0不是 有7天有效"),
):
    try :
        try :
            startIndex = int(startIndex) if startIndex else 1
        except Exception as e:
            startIndex = 1

        is_temp = False if store == '1' else True
        urls = file_service.save_pdf_to_imgs(file, startIndex, is_temp)
        return CommonResult.success(data=urls)
    except Exception as e:
        return CommonResult.error(message='error: %s' % e)
    

@app.post('/ppt_to_pdf', summary="PPT导出为pdf", description="PPT导出为pdf，并上传到cos")
def ppt_to_pdf(
    file: UploadFile = File(...),
    store: Optional[str] = Query(None, description="是否永久文件，1是，非0不是 有7天有效"),
):
    try :
        is_temp = False if store == '1' else True
        url = file_service.ppt_to_pdf(file, is_temp)
        return CommonResult.success(data=url)
    except Exception as e:
        return CommonResult.error(message='error: %s' % e)

@app.post('/asr-corrector', summary="ASR纠错", description="ASR纠错")
def asr_corrector(
    text: str = Form(...),
):
    batch_res = corrector_instance.correct(text)
    return batch_res

# 初始化验证码管理器
verification_code_manager = VerificationCodeManager()

@app.post('/batch_generate_codes', summary="批量生成验证码", description="一次性生成多个验证码")
def batch_generate_codes(
    count: int = Form(500, description="要生成的验证码数量"),
    length: int = Form(6, description="验证码长度"),
    overwrite: bool = Form(True, description="是否覆盖现有验证码，True表示覆盖，False表示追加")
):
    try:
        codes = verification_code_manager.batch_generate_codes(
            count=count,
            expire_seconds=0,  # 无有效期
            length=length,
            description="",    # 无描述
            overwrite=overwrite
        )
        return CommonResult.success(data={
            "count": len(codes),
            "first_10_codes": codes[:10] if len(codes) > 10 else codes,
            "message": f"成功生成{len(codes)}个验证码" + ("并覆盖了原有验证码" if overwrite else "")
        })
    except Exception as e:
        logger.error(f"批量生成验证码错误: {e}")
        return CommonResult.error(message=f'批量生成验证码错误: {e}')

@app.post('/verify_code', summary="验证一次性验证码", description="验证一次性验证码是否有效")
def verify_code(code: str = Form(...)):
    try:
        is_valid, code_record = verification_code_manager.verify_code(code)
        # 直接返回Yes或No
        return CommonResult.success(data="Yes" if is_valid else "No")
    except Exception as e:
        logger.error(f"验证码验证错误: {e}")
        return CommonResult.error(message=f'验证码验证错误: {e}')

@app.get('/remaining_codes', summary="查看剩余验证码", description="查看所有未使用且未过期的验证码")
def remaining_codes():
    try:
        codes_records = verification_code_manager.get_remaining_codes()
        # 只返回验证码字符串数组
        codes = [record["code"] for record in codes_records]
        return CommonResult.success(data={
            "count": len(codes),
            "codes": codes
        })
    except Exception as e:
        logger.error(f"获取剩余验证码错误: {e}")
        return CommonResult.error(message=f'获取剩余验证码错误: {e}')

@app.get('/get_time', summary="延时返回当前时间", description="接收秒数参数t，延时指定秒数后返回当前时间")
def get_time(t: int = Query(0, description="延时秒数")):
    try:
        # 延时指定秒数
        time.sleep(t)
        # 获取当前时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return CommonResult.success(data=current_time)
    except Exception as e:
        logger.error(f"延时返回时间错误: {e}")
        return CommonResult.error(message=f'延时返回时间错误: {e}')


@app.post('/medsci_search', summary="文章查询接口", description="根据条件查询文章信息")
def medsci_search(search_request: SearchRequest):
    """
    文章查询接口
    
    参数说明：
    - module: 检索模块，如article(资讯)、guider(指南)、eda(易迅大)、scale(医学公式)等
    - q: 要检索的内容
    - time_type: 时间筛选，0:所有时间，1:1天内,2:一周内，3:一月内，4:一年内
    - sort_type: 排序条件，1:相关度排序，2:发布时间倒序排
    - search_type: 检索内容，1:全文搜索，2:标题搜索
    
    返回：
    - entity_type: 实体类型
    - entity_id: 实体ID
    """
    try:
        # 调用搜索服务
        result = search_service.search(search_request)
        return result
    except Exception as e:
        logger.error(f"文章查询错误: {e}")
        return CommonResult.error(message=f'文章查询错误: {e}')


# 混合搜索接口已删除