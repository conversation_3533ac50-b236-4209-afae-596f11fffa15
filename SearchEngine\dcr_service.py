import requests
from Utils.moonshot_utils import MoonshotUtils
from Utils.tencent_utils import TencentUtils

class DcrService:
    # AI_BASE_URL = "http://192.168.16.187:48080"
    AI_BASE_URL = "http://apo-bi-server.medsci-bi.svc.cluster.local:8080"

    def get_dcr_hcp(self, pageNo: int = 1, pageSize: int = 10, startTime: str = None, endTime: str = None):
        # url = "http://192.168.16.187:48080/out/dcr-manage/getHcpClean"
        url = DcrService.AI_BASE_URL+"/out/dcr-manage/hcpPage"

        params = {
            "pageNo": pageNo,
            "pageSize": pageSize
        }
        if startTime and endTime:
            params['updateTime'] = [startTime, endTime]

        try:
            response = requests.get(url, params=params)
            response.raise_for_status()  # 如果响应状态码不是200，抛出异常
            return response.json()
        except Exception as e:
            print(f"GET请求失败: {e}")
            return {}
        
    def save_search_res(self, id:int, hco_name: str, hcp_name: str, search_res: str, data_type: str='2'):
        url = DcrService.AI_BASE_URL+"/out/dcr-manage/saveSearchRes"
        data = {
            "id": id,
            "hcoName": hco_name,
            "hcpName": hcp_name,
            "searchRes": search_res,
            "dataType": data_type
        }
        try:
            response = requests.post(url, json=data)
            response.raise_for_status()  # 如果响应状态码不是200，抛出异常
            return response.json()
        except Exception as e:
            print(f"POST请求失败: {e}")
            return None
        
    def clean_hcp(self, pageNo: int = 1, pageSize: int = 10, startTime: str = None, endTime: str = None):
        dcr_hcp = self.get_dcr_hcp(pageNo, pageSize, startTime, endTime)
        print("获取到", dcr_hcp)
        no_list = not dcr_hcp or 'data' not in dcr_hcp  or 'list' not in dcr_hcp.get('data', {})
        if  no_list:
            print("未获取到有效数据")
            return

        dataList = dcr_hcp.get('data').get('list')

        for item in dataList:
            keyword = item.get('hcoName')+' '+item.get('hcpName')
            keyword = "请搜索医生 " + keyword +"""，并根据搜索到的结果提取医院名称、医生名称、科室名称、性别、职称、职务、类型、专长等信息，
       其中最重要的是医院名称、医生名称、科室名称，
       其次是性别、职称，最后是职务、类型(医师、护师、药剂师、技师等)、专长，
       请以JSON字符串格式输出"""
            print(f"搜索: {keyword}")
            # res = MoonshotUtils.search_hcp(keyword)
            res = TencentUtils.chat_sse_client(keyword)
            id = None # item.get('id')
            self.save_search_res(id, item.get('hcoName'), item.get('hcpName'), res)
        
dcr_service = DcrService()