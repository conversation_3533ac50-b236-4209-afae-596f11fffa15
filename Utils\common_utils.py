from datetime import datetime
import uuid
from dotenv import dotenv_values
import hashlib


class CommonUtils:
    MEDSCI_XAI = 'MedSci xAi'
    Ymd = '%Y-%m-%d'
    YmdHM = '%Y-%m-%d %H:%M'
    YmdHMS = '%Y-%m-%d %H:%M:%S'
    Ymd_HMS = '%Y%m%d_%H%M%S'
    Ymd_HMSF = '%Y%m%d_%H%M%S.%f'

    @staticmethod
    def validate_time_format(time_str)->bool:
        """
        验证时间字符串是否符合指定的格式：'%Y-%m-%d', '%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S'
        
        :param time_str: 时间字符串
        :return: 如果格式正确返回True，否则返回False
        """
        # 定义可能的时间格式
        formats = [CommonUtils.Ymd, CommonUtils.YmdHM, CommonUtils.YmdHMS]
        
        # 尝试每个格式
        for fmt in formats:
            try:
                # 尝试解析时间字符串
                datetime.strptime(time_str, fmt)
                # 如果成功解析，返回True
                return True
            except ValueError:
                # 如果解析失败，继续尝试下一个格式
                continue
        
        # 如果所有格式都尝试失败，返回False
        return False
    
    @staticmethod
    def get_time_format(time_str)->str:
        """
        验证时间字符串是否符合指定的格式：'%Y-%m-%d', '%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S'
        
        :param time_str: 时间字符串
        :return: 如果格式正确返回True，否则返回False
        """
        if time_str is None or time_str == '':
            return None

        if CommonUtils.validate_time_format(time_str):
            # 要返回格式 YYYY-MM-DD HH:MM:SS
            time_str = datetime.strptime(time_str, CommonUtils.YmdHMS).strftime(CommonUtils.YmdHMS)
            return time_str
        else:
            return None
        
    @staticmethod
    def get_time_format_or_default(time_str:str=None, format:str=None)->str:
        """
        获取当前时间字符串
        :param time_str: 时间字符串
        :return: 时间字符串
        """
        format = format if format else CommonUtils.YmdHMS
        if time_str is None or time_str == '':
            return datetime.now().strftime(format)
        else:
            res = CommonUtils.get_time_format(time_str)
            if res is None:
                return datetime.now().strftime(format)
            
            return res
        
    @staticmethod
    def get_date_format(time_str)->str:
        """
        验证时间字符串是否符合指定的格式：'%Y-%m-%d', '%Y-%m-%d %H:%M', '%Y-%m-%d %H:%M:%S'
        
        :param time_str: 时间字符串
        :return: 如果格式正确返回True，否则返回False
        """
        if CommonUtils.validate_time_format(time_str):
            # 要返回格式 YYYY-MM-DD
            time_str = datetime.strptime(time_str, CommonUtils.YmdHMS).strftime(CommonUtils.Ymd)
            return time_str
        else:
            return None
        
    @staticmethod
    def get_uuid() -> str:
        """
        生成一个 UUID
        """
        new_uuid = uuid.uuid4()
        session_id = str(new_uuid)
        return session_id
    
    @staticmethod
    def get_env(key:str, default:str=None) -> str:
        """
        获取环境变量
        """
        env = dotenv_values()
        return env.get(key, default)
    
    @staticmethod
    def get_md5(content:str)->str:
        """
        获取md5,返回最终的16进制Hash值
        """
        hl = hashlib.md5()
        hl.update(content.encode(encoding='utf-8'))
        return hl.hexdigest()
