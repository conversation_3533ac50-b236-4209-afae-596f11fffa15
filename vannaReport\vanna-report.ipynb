{"cells": [{"cell_type": "code", "execution_count": 8, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:50:46.737380Z", "start_time": "2025-03-04T11:50:46.609359Z"}, "collapsed": true}, "outputs": [{"data": {"text/plain": ["<__main__.<PERSON><PERSON><PERSON> at 0x12ea8d190>"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# 初始化 vanna\n", "from vanna.qianwen.QianwenAI_chat import QianWenAI_Chat\n", "from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore\n", "\n", "class MyVanna(ChromaDB_VectorStore, QianWenAI_Chat):\n", "    def __init__(self, config=None):\n", "        ChromaDB_VectorStore.__init__(self, config=config)\n", "        QianWenAI_Chat.__init__(self, config=config)\n", "\n", "    def generate_query_explanation(self, sql: str):\n", "        my_prompt = [\n", "            self.system_message(\"You are a helpful assistant that will explain a SQL query!\"),\n", "            self.user_message(\"请用一句话解释SQL: \" + sql),\n", "        ]\n", "\n", "        return self.submit_prompt(prompt=my_prompt)\n", "\n", "vn = MyVanna(config={'api_key': 'sk-c78e30cddfdc4b90b7f9f45497421b08', 'model': 'qwen-max-latest'})\n", "vn"]}, {"cell_type": "code", "execution_count": 9, "id": "3d14504973716143", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:04:05.098953Z", "start_time": "2025-03-04T11:04:02.890925Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["('v1.2.0', 74)\n"]}], "source": ["# 使用duckdb链接pgsql\n", "import duckdb\n", "\n", "conn = duckdb.connect('ai-base-report-datasource.duckdb')\n", "conn.execute(\"INSTALL postgres;\")\n", "conn.execute(\"LOAD postgres;\")\n", "conn.execute(\"\"\"\n", "    ATTACH '*******************************************/dify_base'\n", "    AS pg_db (TYPE postgres);\n", "\"\"\")\n", "# 查询 PostgreSQL 数据\n", "result = conn.execute(\"SELECT version(), count(1)  FROM pg_db.ai_base.ai_app_langs\").fetchone()\n", "print(result)"]}, {"cell_type": "code", "execution_count": 10, "id": "d6b2f139bfb5d8c7", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:04:06.805431Z", "start_time": "2025-03-04T11:04:06.463643Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count(1)</th>\n", "      <th>min(\"访问时间\")</th>\n", "      <th>max(\"访问时间\")</th>\n", "      <th>count(DISTINCT \"用户ID\")</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>11879</td>\n", "      <td>2024-11-12 17:55:27.529904</td>\n", "      <td>2025-04-02 13:59:23.609931</td>\n", "      <td>10</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   count(1)                min(\"访问时间\")                max(\"访问时间\")  \\\n", "0     11879 2024-11-12 17:55:27.529904 2025-04-02 13:59:23.609931   \n", "\n", "   count(DISTINCT \"用户ID\")  \n", "0                      10  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将数据从pgsql导入到duckdb\n", "conn.execute(\"DROP TABLE IF EXISTS main.aibase_report_datasource;\")\n", "conn.execute(\"\"\"\n", "    CREATE TABLE main.aibase_report_datasource AS\n", "            select\n", "                CASE when log.行为 = '访问首页' then '访问首页'\n", "                    else app.app_name\n", "                end as 应用名,\n", "                log.*\n", "            from (\n", "                 select\n", "                     user_id 用户ID,\n", "                     CASE user_type\n", "                         WHEN 37 THEN 'MedSci xAI'\n", "                         WHEN 0 THEN '主站'\n", "                         WHEN 36 THEN 'Facebook'\n", "                         WHEN 35 THEN 'Google'\n", "                         ELSE CAST(user_type AS TEXT)\n", "                         END AS 用户类型,\n", "                     case request_url\n", "                         WHEN '/ai-base/index/getAppTypes' THEN '访问首页'\n", "                         ELSE '聊天互动'\n", "                         END AS 行为,\n", "                     ((request_params::json)->>'body')::json->>'appUuid' AS app_uuid,\n", "                     CASE\n", "                         WHEN user_agent ILIKE '%Mobile%'\n", "                             OR user_agent ILIKE '%Android%'\n", "                             OR user_agent ILIKE '%iPhone%'\n", "                             OR user_agent ILIKE '%iPad%'\n", "                             OR user_agent ILIKE '%Windows Phone%'\n", "                             THEN '移动端'\n", "                         ELSE 'PC端'\n", "                     END AS 设备类型,\n", "                     duration 执行时长,\n", "                     result_code 结果码,\n", "                     create_time 访问时间\n", "                 from pg_db.yudao.infra_api_access_log log\n", "                 where request_url in ('/ai-base/index/getAppTypes', '/ai-base/chat/chat-messages', '/ai-base/chat/workflows/run', '/ai-base/chat/completion-messages',\n", "                     '/ai-base/yps-chat/chat-messages', '/ai-base/yps-chat/completion-messages', '/ai-base/yps-chat/workflows/run1')\n", "            )log\n", "            left join pg_db.ai_base.ai_app_langs app on log.app_uuid <> '' and app.app_uuid::text = log.app_uuid;\n", "        \"\"\")\n", "conn.commit()\n", "\n", "\n", "# 测速数据是否导入成功\n", "import_test = conn.execute(\"select count(1), min(访问时间), max(访问时间), count(DISTINCT 用户ID) from main.aibase_report_datasource limit 3\").fetchdf()\n", "import_test"]}, {"cell_type": "code", "execution_count": 11, "id": "425972c7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["False\n"]}], "source": ["vn.connect_to_duckdb(url='file:ai-base-report-datasource.duckdb') "]}, {"cell_type": "code", "execution_count": null, "id": "ab760024c1160529", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T10:59:17.703700Z", "start_time": "2025-03-04T10:59:12.540200Z"}}, "outputs": [], "source": ["# 训练\n", "vn.train(ddl=\"\"\"\n", "    CREATE TABLE main.aibase_report_datasource (\n", "        应用名 VARCHAR(36) COMMENT '应用名称',\n", "        用户ID int COMMENT '用户的ID',\n", "        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\n", "        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\n", "        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\n", "        执行时长 int COMMENT '接口执行耗时, 单位ms',\n", "        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\n", "        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\n", "    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\n", "\"\"\")\n", "\n", "\n", "vn.train(sql=\"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\",\n", "         question=\"哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？\")\n", "\n", "vn.train(sql=\"SELECT 用户ID, 行为, 访问时间, RANK() OVER (PARTITION BY 用户ID ORDER BY 访问时间 DESC) as 时间排名 FROM main.aibase_report_datasource WHERE 设备类型 = '移动端' AND 执行时长 > 200;\",\n", "         question=\"每个用户在移动端的最近行为是什么，且执行时长超过200ms？\")\n", "\n", "vn.train(sql=\"SELECT 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 执行时长 ELSE 0 END) / NULLIF(SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END), 0) as 成功平均时长 FROM main.aibase_report_datasource GROUP BY 用户类型, 设备类型 ORDER BY 成功平均时长 DESC;\",\n", "         question=\"按用户类型和设备类型统计成功的平均执行时长，并按时长降序排列？\")\n", "\n", "vn.train(sql=\"SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);\",\n", "         question=\"哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？\")\n", "\n", "vn.train(sql=\"\"\"\n", "                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\n", "                       \"应用名\",\n", "                       COUNT(*) AS 访问次数\n", "                FROM main.aibase_report_datasource\n", "                WHERE \"用户类型\" IN ('MedSci xAI', 'Facebook')\n", "                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\n", "                HAVING COUNT(*) > 10\n", "                ORDER BY 日期 DESC, 访问次数 DESC;\"\"\",\n", "         question=\"每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？\")\n", "\n", "vn.train(sql=\"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\",\n", "         question=\"哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？\")\n", "\n", "vn.train(sql=\"SELECT 应用名, 设备类型, SUM(执行时长) as 总时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 应用名 IN (SELECT 应用名 FROM main.aibase_report_datasource WHERE 结果码 > 0 GROUP BY 应用名 HAVING COUNT(*) > 5) GROUP BY 应用名, 设备类型;\",\n", "         question=\"哪些失败超过5次的应用在成功时的总执行时长是多少，按设备类型分组？\")\n", "\n", "vn.train(sql=\"\"\"SELECT\n", "                    \"用户类型\",\n", "                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\n", "                    AVG(\"执行时长\") AS 平均时长\n", "                FROM\n", "                    main.aibase_report_datasource\n", "                WHERE\n", "                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\n", "                GROUP BY\n", "                    \"用户类型\"\n", "                ORDER BY\n", "                    活跃用户数 DESC;\"\"\",\n", "         question=\"过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？\")\n", "\n", "vn.train(sql=\"SELECT a.用户ID, a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.执行时长 > (SELECT AVG(执行时长) * 1.5 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0);\",\n", "         question=\"哪些用户的执行时长超过其所在应用平均成功时长的1.5倍？\")\n", "\n", "vn.train(sql=\"SELECT 应用名, 用户类型, ROW_NUMBER() OVER (PARTITION BY 应用名 ORDER BY COUNT(*) DESC) as 排名, COUNT(*) as 访问次数 FROM main.aibase_report_datasource GROUP BY 应用名, 用户类型 HAVING 访问次数 > 3;\",\n", "         question=\"每个应用中按用户类型排名的访问次数是多少，筛选访问次数大于3的记录？\")\n", "\n", "vn.train(sql=\"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\",\n", "         question=\"每个设备类型在每天各小时成功访问首页的次数是多少？\")\n", "\n", "vn.train(sql=\"SELECT 用户ID, 应用名, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-02-01' GROUP BY 用户ID, 应用名 HAVING 成功次数 > 失败次数;\",\n", "         question=\"2025年2月后哪些用户在每个应用中的成功次数超过失败次数？\")\n", "\n", "vn.train(sql=\"SELECT 应用名, MAX(执行时长) - MIN(执行时长) as 时长差 FROM main.aibase_report_datasource WHERE 用户类型 = 'Google' AND 结果码 = 0 GROUP BY 应用名 HAVING 时长差 > 1000;\",\n", "         question=\"Google用户在哪些应用中成功执行时长的最大差值超过1000ms？\")\n", "\n", "vn.train(sql=\"SELECT a.应用名, a.用户类型, a.访问时间 FROM main.aibase_report_datasource a WHERE a.执行时长 = (SELECT MAX(执行时长) FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.用户类型 = a.用户类型);\",\n", "         question=\"每个应用和用户类型组合中最长执行时长的记录是什么？\")\n", "\n", "vn.train(sql=\"\"\"SELECT\n", "                    \"用户类型\",\n", "                    \"设备类型\",\n", "                    COUNT(*) AS 次数,\n", "                    PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY \"执行时长\") AS 时长95分位\n", "                FROM\n", "                    main.aibase_report_datasource\n", "                WHERE\n", "                    \"结果码\" = 0\n", "                GROUP BY\n", "                    \"用户类型\",\n", "                    \"设备类型\";\"\"\",\n", "         question=\"每个用户类型和设备类型的成功访问次数及执行时长的95分位值是多少？\")\n", "\n", "vn.train(sql=\"SELECT 用户ID, 应用名, 访问时间, LAG(执行时长, 1) OVER (PARTITION BY 用户ID ORDER BY 访问时间) as 上次时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 设备类型 = 'PC端';\",\n", "         question=\"每个用户在PC端成功访问时，上一次的执行时长是多少？\")\n", "\n", "vn.train(sql=\"SELECT 应用名, 用户类型, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 执行时长 > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0) * 2 GROUP BY 应用名, 用户类型;\",\n", "         question=\"哪些应用和用户类型的访问执行时长超过全局成功平均时长的2倍，返回访问次数？\")\n", "\n", "vn.train(sql=\"SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);\",\n", "         question=\"每个用户第一次失败访问的记录是什么？\")\n", "\n", "vn.train(sql=\"SELECT 应用名, 用户类型, 设备类型, SUM(执行时长) as 总时长, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' AND 用户ID IN (SELECT 用户ID FROM main.aibase_report_datasource WHERE 结果码 > 0 GROUP BY 用户ID HAVING COUNT(*) > 3) GROUP BY 应用名, 用户类型, 设备类型 ORDER BY 总时长 DESC;\",\n", "         question=\"2025年后，失败超过3次的用户在每个应用、用户类型和设备类型中的总执行时长和访问次数是多少？\")\n", "\n", "vn.train(sql=\"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\", question=\"2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？\")\n", "\n", "vn.train(sql=\"SELECT * FROM main.aibase_report_datasource WHERE 应用名 = '问题分级引导流程' AND CAST('访问时间' AS DATE) = CURRENT_DATE;\", \n", "         question=\"应用 问题分级引导流程 今天的访问记录\")"]}, {"cell_type": "code", "execution_count": 13, "id": "15f550b4", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Insert of existing embedding ID: 064f207b-c78a-5e84-8df0-dae41faaadf1-sql\n", "Add of existing embedding ID: 064f207b-c78a-5e84-8df0-dae41faaadf1-sql\n"]}, {"data": {"text/plain": ["'064f207b-c78a-5e84-8df0-dae41faaadf1-sql'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["vn.train(sql=\"SELECT * FROM main.aibase_report_datasource WHERE 应用名 = '问题分级引导流程' AND CAST('访问时间' AS DATE) = CURRENT_DATE;\", \n", "         question=\"应用 问题分级引导流程 今天的访问记录\")"]}, {"cell_type": "code", "execution_count": 14, "id": "62495b41", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '今天的 问题分级引导流程 访问记录详情是什么？'}, {'role': 'assistant', 'content': \"SELECT * FROM main.aibase_report_datasource WHERE 应用名 = '问题分级引导流程' AND CAST('访问时间' AS DATE) = CURRENT_DATE;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '应用 问题分级引导流程 今天的访问记录'}, {'role': 'assistant', 'content': \"SELECT * FROM main.aibase_report_datasource WHERE 应用名 = '问题分级引导流程' AND CAST('访问时间' AS DATE) = CURRENT_DATE;\"}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 970.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "          日期  访问次数\n", "0 2025-03-05   131\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Insert of existing embedding ID: bf061c5d-fad7-5977-9dd3-41505c99198a-sql\n", "Add of existing embedding ID: bf061c5d-fad7-5977-9dd3-41505c99198a-sql\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Using model qwen-max-latest for 199.5 tokens (approx)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"data": {"image/png": "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", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '今天的 问题分级引导流程 访问记录详情是什么？'}, {'role': 'assistant', 'content': \"SELECT * FROM main.aibase_report_datasource WHERE 应用名 = '问题分级引导流程' AND CAST('访问时间' AS DATE) = CURRENT_DATE;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '应用 问题分级引导流程 今天的访问记录'}, {'role': 'assistant', 'content': \"SELECT * FROM main.aibase_report_datasource WHERE 应用名 = '问题分级引导流程' AND CAST('访问时间' AS DATE) = CURRENT_DATE;\"}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 970.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": ["vn.ask('首页被访问最多的一次是哪一天, 当天一共被访问了多少次?')\n", "test_question = '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'\n", "ask_sql = vn.generate_sql(test_question)"]}, {"cell_type": "code", "execution_count": 14, "id": "e792fdd93e73a18b", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:44:04.531843Z", "start_time": "2025-03-04T11:44:04.525651Z"}}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 校验是否合法\n", "vn.is_sql_valid(sql=ask_sql)"]}, {"cell_type": "code", "execution_count": null, "id": "41ec7fd6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": ["# 生成SQL\n", "test_question = '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'\n", "ask_sql = vn.generate_sql(test_question)"]}, {"cell_type": "code", "execution_count": 15, "id": "f388dc94", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": ["# 生成SQL\n", "test_question = '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'\n", "ask_sql = vn.generate_sql(test_question)"]}, {"cell_type": "code", "execution_count": 16, "id": "a12e15db", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": ["# 生成SQL\n", "test_question = '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'\n", "ask_sql = vn.generate_sql(test_question)"]}, {"cell_type": "code", "execution_count": 17, "id": "4ca73b95bd55e8c2", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:51:00.268461Z", "start_time": "2025-03-04T11:50:56.620313Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using model qwen-max-latest for 58.5 tokens (approx)\n"]}, {"data": {"text/plain": ["'这条SQL查询的作用是：从`main.aibase_report_datasource`表中筛选出“行为”为“访问首页”的记录，按“访问时间”转换为日期格式后分组，统计每天的访问次数，并按访问次数降序排列，最终返回访问次数最多的一天及其对应的访问次数。'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# 生成sql解释\n", "sql_explanation = vn.generate_query_explanation(ask_sql)\n", "sql_explanation"]}, {"cell_type": "code", "execution_count": 18, "id": "5660cf05c99a72e7", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:53:08.572485Z", "start_time": "2025-03-04T11:53:08.562800Z"}}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>日期</th>\n", "      <th>访问次数</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2025-03-05</td>\n", "      <td>131</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          日期  访问次数\n", "0 2025-03-05   131"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["df = vn.run_sql(sql=ask_sql)\n", "df"]}, {"cell_type": "code", "execution_count": 19, "id": "2a0d2522db70a01a", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:44:31.704680Z", "start_time": "2025-03-04T11:44:24.398124Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using model qwen-max-latest for 187.5 tokens (approx)\n"]}, {"data": {"text/plain": ["'import plotly.graph_objects as go\\n\\nfig = go.Figure(go.Indicator(\\n    mode = \"number\",\\n    value = df[\\'访问次数\\'].iloc[0],\\n    title = {\"text\": f\"Most Visits on {df[\\'日期\\'].iloc[0]}\"},\\n    domain = {\\'x\\': [0, 1], \\'y\\': [0, 1]}\\n))\\n\\n'"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["plotly_code = vn.generate_plotly_code(question= test_question, sql=ask_sql, df_metadata=df)\n", "plotly_code"]}, {"cell_type": "code", "execution_count": null, "id": "8a5b8d792a25bbc1", "metadata": {"ExecuteTime": {"end_time": "2025-03-04T11:45:39.724611Z", "start_time": "2025-03-04T11:45:39.655836Z"}}, "outputs": [{"ename": "ValueError", "evalue": "Mime type rendering requires nbformat>=4.2.0 but it is not installed", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/ai-base/lib/python3.11/site-packages/IPython/core/formatters.py:984\u001b[39m, in \u001b[36mIPythonDisplayFormatter.__call__\u001b[39m\u001b[34m(self, obj)\u001b[39m\n\u001b[32m    982\u001b[39m method = get_real_method(obj, \u001b[38;5;28mself\u001b[39m.print_method)\n\u001b[32m    983\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m method \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m984\u001b[39m     \u001b[43mmethod\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    985\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/ai-base/lib/python3.11/site-packages/plotly/basedatatypes.py:833\u001b[39m, in \u001b[36mBaseFigure._ipython_display_\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    830\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplotly\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mio\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpio\u001b[39;00m\n\u001b[32m    832\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m pio.renderers.render_on_display \u001b[38;5;129;01mand\u001b[39;00m pio.renderers.default:\n\u001b[32m--> \u001b[39m\u001b[32m833\u001b[39m     \u001b[43mpio\u001b[49m\u001b[43m.\u001b[49m\u001b[43mshow\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    834\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    835\u001b[39m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;28mrepr\u001b[39m(\u001b[38;5;28mself\u001b[39m))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/miniconda3/envs/ai-base/lib/python3.11/site-packages/plotly/io/_renderers.py:425\u001b[39m, in \u001b[36mshow\u001b[39m\u001b[34m(fig, renderer, validate, **kwargs)\u001b[39m\n\u001b[32m    420\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    421\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mMime type rendering requires ipython but it is not installed\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    422\u001b[39m     )\n\u001b[32m    424\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m nbformat \u001b[38;5;129;01mor\u001b[39;00m Version(nbformat.__version__) < Version(\u001b[33m\"\u001b[39m\u001b[33m4.2.0\u001b[39m\u001b[33m\"\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m425\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    426\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mMime type rendering requires nbformat>=4.2.0 but it is not installed\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    427\u001b[39m     )\n\u001b[32m    429\u001b[39m display_jupyter_version_warnings()\n\u001b[32m    431\u001b[39m ipython_display.display(bundle, raw=\u001b[38;5;28;01mTrue\u001b[39;00m)\n", "\u001b[31mValueError\u001b[39m: Mime type rendering requires nbformat>=4.2.0 but it is not installed"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"domain": {"x": [0, 1], "y": [0, 1]}, "mode": "number", "title": {"text": "Most Visits on 2025-03-05 00:00:00"}, "type": "indicator", "value": 131}], "layout": {"template": {"data": {"bar": [{"error_x": {"color": "#f2f5fa"}, "error_y": {"color": "#f2f5fa"}, "marker": {"line": {"color": "rgb(17,17,17)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "rgb(17,17,17)", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#506784", "linecolor": "#506784", "minorgridcolor": "#506784", "startlinecolor": "#A2B1C6"}, "baxis": {"endlinecolor": "#A2B1C6", "gridcolor": "#506784", "linecolor": "#506784", "minorgridcolor": "#506784", "startlinecolor": "#A2B1C6"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"marker": {"line": {"color": "#283442"}}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"line": {"color": "#283442"}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#506784"}, "line": {"color": "rgb(17,17,17)"}}, "header": {"fill": {"color": "#2a3f5f"}, "line": {"color": "rgb(17,17,17)"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#f2f5fa", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#f2f5fa"}, "geo": {"bgcolor": "rgb(17,17,17)", "lakecolor": "rgb(17,17,17)", "landcolor": "rgb(17,17,17)", "showlakes": true, "showland": true, "subunitcolor": "#506784"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "dark"}, "paper_bgcolor": "rgb(17,17,17)", "plot_bgcolor": "rgb(17,17,17)", "polar": {"angularaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "bgcolor": "rgb(17,17,17)", "radialaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}, "yaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}, "zaxis": {"backgroundcolor": "rgb(17,17,17)", "gridcolor": "#506784", "gridwidth": 2, "linecolor": "#506784", "showbackground": true, "ticks": "", "zerolinecolor": "#C8D4E3"}}, "shapedefaults": {"line": {"color": "#f2f5fa"}}, "sliderdefaults": {"bgcolor": "#C8D4E3", "bordercolor": "rgb(17,17,17)", "borderwidth": 1, "tickwidth": 0}, "ternary": {"aaxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "baxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}, "bgcolor": "rgb(17,17,17)", "caxis": {"gridcolor": "#506784", "linecolor": "#506784", "ticks": ""}}, "title": {"x": 0.05}, "updatemenudefaults": {"bgcolor": "#506784", "borderwidth": 0}, "xaxis": {"automargin": true, "gridcolor": "#283442", "linecolor": "#506784", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#283442", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "#283442", "linecolor": "#506784", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "#283442", "zerolinewidth": 2}}}}}, "text/html": ["<div>                        <script type=\"text/javascript\">window.PlotlyConfig = {MathJaxConfig: 'local'};</script>\n", "        <script charset=\"utf-8\" src=\"https://cdn.plot.ly/plotly-3.0.0.min.js\"></script>                <div id=\"531826fe-8f97-42b0-aa92-343f081ffba1\" class=\"plotly-graph-div\" style=\"height:100%; width:100%;\"></div>            <script type=\"text/javascript\">                window.PLOTLYENV=window.PLOTLYENV || {};                                if (document.getElementById(\"531826fe-8f97-42b0-aa92-343f081ffba1\")) {                    Plotly.newPlot(                        \"531826fe-8f97-42b0-aa92-343f081ffba1\",                        [{\"domain\":{\"x\":[0,1],\"y\":[0,1]},\"mode\":\"number\",\"title\":{\"text\":\"Most Visits on 2025-03-05 00:00:00\"},\"value\":131,\"type\":\"indicator\"}],                        {\"template\":{\"data\":{\"barpolar\":[{\"marker\":{\"line\":{\"color\":\"rgb(17,17,17)\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"barpolar\"}],\"bar\":[{\"error_x\":{\"color\":\"#f2f5fa\"},\"error_y\":{\"color\":\"#f2f5fa\"},\"marker\":{\"line\":{\"color\":\"rgb(17,17,17)\",\"width\":0.5},\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"bar\"}],\"carpet\":[{\"aaxis\":{\"endlinecolor\":\"#A2B1C6\",\"gridcolor\":\"#506784\",\"linecolor\":\"#506784\",\"minorgridcolor\":\"#506784\",\"startlinecolor\":\"#A2B1C6\"},\"baxis\":{\"endlinecolor\":\"#A2B1C6\",\"gridcolor\":\"#506784\",\"linecolor\":\"#506784\",\"minorgridcolor\":\"#506784\",\"startlinecolor\":\"#A2B1C6\"},\"type\":\"carpet\"}],\"choropleth\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"type\":\"choropleth\"}],\"contourcarpet\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"type\":\"contourcarpet\"}],\"contour\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"type\":\"contour\"}],\"heatmap\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"type\":\"heatmap\"}],\"histogram2dcontour\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"type\":\"histogram2dcontour\"}],\"histogram2d\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"type\":\"histogram2d\"}],\"histogram\":[{\"marker\":{\"pattern\":{\"fillmode\":\"overlay\",\"size\":10,\"solidity\":0.2}},\"type\":\"histogram\"}],\"mesh3d\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"type\":\"mesh3d\"}],\"parcoords\":[{\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"parcoords\"}],\"pie\":[{\"automargin\":true,\"type\":\"pie\"}],\"scatter3d\":[{\"line\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scatter3d\"}],\"scattercarpet\":[{\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scattercarpet\"}],\"scattergeo\":[{\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scattergeo\"}],\"scattergl\":[{\"marker\":{\"line\":{\"color\":\"#283442\"}},\"type\":\"scattergl\"}],\"scattermapbox\":[{\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scattermapbox\"}],\"scattermap\":[{\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scattermap\"}],\"scatterpolargl\":[{\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scatterpolargl\"}],\"scatterpolar\":[{\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scatterpolar\"}],\"scatter\":[{\"marker\":{\"line\":{\"color\":\"#283442\"}},\"type\":\"scatter\"}],\"scatterternary\":[{\"marker\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"type\":\"scatterternary\"}],\"surface\":[{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"},\"colorscale\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"type\":\"surface\"}],\"table\":[{\"cells\":{\"fill\":{\"color\":\"#506784\"},\"line\":{\"color\":\"rgb(17,17,17)\"}},\"header\":{\"fill\":{\"color\":\"#2a3f5f\"},\"line\":{\"color\":\"rgb(17,17,17)\"}},\"type\":\"table\"}]},\"layout\":{\"annotationdefaults\":{\"arrowcolor\":\"#f2f5fa\",\"arrowhead\":0,\"arrowwidth\":1},\"autotypenumbers\":\"strict\",\"coloraxis\":{\"colorbar\":{\"outlinewidth\":0,\"ticks\":\"\"}},\"colorscale\":{\"diverging\":[[0,\"#8e0152\"],[0.1,\"#c51b7d\"],[0.2,\"#de77ae\"],[0.3,\"#f1b6da\"],[0.4,\"#fde0ef\"],[0.5,\"#f7f7f7\"],[0.6,\"#e6f5d0\"],[0.7,\"#b8e186\"],[0.8,\"#7fbc41\"],[0.9,\"#4d9221\"],[1,\"#276419\"]],\"sequential\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]],\"sequentialminus\":[[0.0,\"#0d0887\"],[0.1111111111111111,\"#46039f\"],[0.2222222222222222,\"#7201a8\"],[0.3333333333333333,\"#9c179e\"],[0.4444444444444444,\"#bd3786\"],[0.5555555555555556,\"#d8576b\"],[0.6666666666666666,\"#ed7953\"],[0.7777777777777778,\"#fb9f3a\"],[0.8888888888888888,\"#fdca26\"],[1.0,\"#f0f921\"]]},\"colorway\":[\"#636efa\",\"#EF553B\",\"#00cc96\",\"#ab63fa\",\"#FFA15A\",\"#19d3f3\",\"#FF6692\",\"#B6E880\",\"#FF97FF\",\"#FECB52\"],\"font\":{\"color\":\"#f2f5fa\"},\"geo\":{\"bgcolor\":\"rgb(17,17,17)\",\"lakecolor\":\"rgb(17,17,17)\",\"landcolor\":\"rgb(17,17,17)\",\"showlakes\":true,\"showland\":true,\"subunitcolor\":\"#506784\"},\"hoverlabel\":{\"align\":\"left\"},\"hovermode\":\"closest\",\"mapbox\":{\"style\":\"dark\"},\"paper_bgcolor\":\"rgb(17,17,17)\",\"plot_bgcolor\":\"rgb(17,17,17)\",\"polar\":{\"angularaxis\":{\"gridcolor\":\"#506784\",\"linecolor\":\"#506784\",\"ticks\":\"\"},\"bgcolor\":\"rgb(17,17,17)\",\"radialaxis\":{\"gridcolor\":\"#506784\",\"linecolor\":\"#506784\",\"ticks\":\"\"}},\"scene\":{\"xaxis\":{\"backgroundcolor\":\"rgb(17,17,17)\",\"gridcolor\":\"#506784\",\"gridwidth\":2,\"linecolor\":\"#506784\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"#C8D4E3\"},\"yaxis\":{\"backgroundcolor\":\"rgb(17,17,17)\",\"gridcolor\":\"#506784\",\"gridwidth\":2,\"linecolor\":\"#506784\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"#C8D4E3\"},\"zaxis\":{\"backgroundcolor\":\"rgb(17,17,17)\",\"gridcolor\":\"#506784\",\"gridwidth\":2,\"linecolor\":\"#506784\",\"showbackground\":true,\"ticks\":\"\",\"zerolinecolor\":\"#C8D4E3\"}},\"shapedefaults\":{\"line\":{\"color\":\"#f2f5fa\"}},\"sliderdefaults\":{\"bgcolor\":\"#C8D4E3\",\"bordercolor\":\"rgb(17,17,17)\",\"borderwidth\":1,\"tickwidth\":0},\"ternary\":{\"aaxis\":{\"gridcolor\":\"#506784\",\"linecolor\":\"#506784\",\"ticks\":\"\"},\"baxis\":{\"gridcolor\":\"#506784\",\"linecolor\":\"#506784\",\"ticks\":\"\"},\"bgcolor\":\"rgb(17,17,17)\",\"caxis\":{\"gridcolor\":\"#506784\",\"linecolor\":\"#506784\",\"ticks\":\"\"}},\"title\":{\"x\":0.05},\"updatemenudefaults\":{\"bgcolor\":\"#506784\",\"borderwidth\":0},\"xaxis\":{\"automargin\":true,\"gridcolor\":\"#283442\",\"linecolor\":\"#506784\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"#283442\",\"zerolinewidth\":2},\"yaxis\":{\"automargin\":true,\"gridcolor\":\"#283442\",\"linecolor\":\"#506784\",\"ticks\":\"\",\"title\":{\"standoff\":15},\"zerolinecolor\":\"#283442\",\"zerolinewidth\":2}}}},                        {\"responsive\": true}                    )                };            </script>        </div>"], "text/plain": ["Figure({\n", "    'data': [{'domain': {'x': [0, 1], 'y': [0, 1]},\n", "              'mode': 'number',\n", "              'title': {'text': 'Most Visits on 2025-03-05 00:00:00'},\n", "              'type': 'indicator',\n", "              'value': 131}],\n", "    'layout': {'template': '...'}\n", "})"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["fig = vn.get_plotly_figure(plotly_code=plotly_code, df=df)\n", "fig"]}, {"cell_type": "code", "execution_count": null, "id": "54a183cd", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7c02677a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7aff8e77", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4a864617", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "bccc7db7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "13c7fadc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cb153361", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cce046ad", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "6cc68a37", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "afda1fa5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3587a7cc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 10 is greater than number of elements in index 1, updating n_results = 1\n"]}, {"name": "stdout", "output_type": "stream", "text": ["SQL Prompt: [{'role': 'system', 'content': \"You are a DuckDB SQL expert. Please help to generate a SQL query to answer the question. Your response should ONLY be based on the given context and follow the response guidelines and format instructions. \\n===Tables \\n\\n    CREATE TABLE main.aibase_report_datasource (\\n        应用名 VARCHAR(36) COMMENT '应用名称',\\n        用户ID int COMMENT '用户的ID',\\n        用户类型 VARCHAR(36) COMMENT '用户来自哪个平台(MedSci xAI // 主站 // Facebook // Google)',\\n        行为 VARCHAR(15) COMMENT '用户行为 (聊天互动 // 访问首页)',\\n        设备类型 VARCHAR(6) COMMENT '用户访问的设备类型 (PC端 // 移动端)',\\n        执行时长 int COMMENT '接口执行耗时, 单位ms',\\n        结果码 int COMMENT '接口执行返回的状态值, 成功为0, 大于0为具体错误码',\\n        访问时间 DATETIME COMMENT '用户访问时间, 格式为 YYYY-MM-DD HH:MM:SS'\\n    ) COMMENT 'AI BASE REPORT DATASOURCE 梅斯小智统计报表数据源';\\n\\n\\n===Response Guidelines \\n1. If the provided context is sufficient, please generate a valid SQL query without any explanations for the question. \\n2. If the provided context is almost sufficient but requires knowledge of a specific string in a particular column, please generate an intermediate SQL query to find the distinct strings in that column. Prepend the query with a comment saying intermediate_sql \\n3. If the provided context is insufficient, please explain why it can't be generated. \\n4. Please use the most relevant table(s). \\n5. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \\n6. Ensure that the output SQL is DuckDB SQL-compliant and executable, and free of syntax errors. \\n\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}, {'role': 'assistant', 'content': 'SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\nFROM main.aibase_report_datasource\\nWHERE \"行为\" = \\'访问首页\\'\\nGROUP BY CAST(\"访问时间\" AS DATE)\\nORDER BY 访问次数 DESC\\nLIMIT 1;'}, {'role': 'user', 'content': '每个设备类型在每天各小时成功访问首页的次数是多少？'}, {'role': 'assistant', 'content': \"SELECT 设备类型, HOUR(访问时间) as 小时, COUNT(*) as 访问次数 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 行为 = '访问首页' GROUP BY 设备类型, HOUR(访问时间) ORDER BY 小时 ASC;\"}, {'role': 'user', 'content': '过去30天每个用户类型的活跃用户数和平均执行时长是多少，按活跃用户数排序？'}, {'role': 'assistant', 'content': 'SELECT\\n                    \"用户类型\",\\n                    COUNT(DISTINCT \"用户ID\") AS 活跃用户数,\\n                    AVG(\"执行时长\") AS 平均时长\\n                FROM\\n                    main.aibase_report_datasource\\n                WHERE\\n                    \"访问时间\" >= CURRENT_DATE - INTERVAL 30 DAY\\n                GROUP BY\\n                    \"用户类型\"\\n                ORDER BY\\n                    活跃用户数 DESC;'}, {'role': 'user', 'content': '平均每天首页被访问多少次?'}, {'role': 'assistant', 'content': 'SELECT AVG(访问次数) AS 平均每天访问次数\\nFROM (\\n    SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\\n    FROM main.aibase_report_datasource\\n    WHERE \"行为\" = \\'访问首页\\'\\n    GROUP BY CAST(\"访问时间\" AS DATE)\\n);'}, {'role': 'user', 'content': '哪些用户在聊天互动中使用了多种设备类型，返回他们的最近访问时间和最短执行时长？'}, {'role': 'assistant', 'content': \"SELECT 用户ID, MAX(访问时间) as 最近访问, MIN(执行时长) as 最短时长 FROM main.aibase_report_datasource WHERE 行为 = '聊天互动' GROUP BY 用户ID HAVING COUNT(DISTINCT 设备类型) > 1;\"}, {'role': 'user', 'content': '每天每个应用来自MedSci xAI或Facebook的访问次数超过10次的记录是什么？'}, {'role': 'assistant', 'content': '\\n                SELECT CAST(\"访问时间\" AS DATE) AS 日期,\\n                       \"应用名\",\\n                       COUNT(*) AS 访问次数\\n                FROM main.aibase_report_datasource\\n                WHERE \"用户类型\" IN (\\'MedSci xAI\\', \\'Facebook\\')\\n                GROUP BY CAST(\"访问时间\" AS DATE), \"应用名\"\\n                HAVING COUNT(*) > 10\\n                ORDER BY 日期 DESC, 访问次数 DESC;'}, {'role': 'user', 'content': '哪些应用在成功访问(结果码=0)后又出现了失败记录(结果码>0)？'}, {'role': 'assistant', 'content': 'SELECT a.应用名, a.访问时间, a.执行时长 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND EXISTS (SELECT 1 FROM main.aibase_report_datasource b WHERE b.应用名 = a.应用名 AND b.结果码 = 0 AND b.访问时间 < a.访问时间);'}, {'role': 'user', 'content': '2025年后，每个应用、用户类型和设备类型中的成功次数超过失败次数的记录是什么？'}, {'role': 'assistant', 'content': \"SELECT 应用名, 用户类型, 设备类型, SUM(CASE WHEN 结果码 = 0 THEN 1 ELSE 0 END) as 成功次数, SUM(CASE WHEN 结果码 > 0 THEN 1 ELSE 0 END) as 失败次数 FROM main.aibase_report_datasource WHERE 访问时间 >= '2025-01-01' GROUP BY 应用名, 用户类型, 设备类型 HAVING 成功次数 > 失败次数;\"}, {'role': 'user', 'content': '每个用户第一次失败访问的记录是什么？'}, {'role': 'assistant', 'content': 'SELECT a.用户ID, a.应用名, a.访问时间 FROM main.aibase_report_datasource a WHERE a.结果码 > 0 AND a.访问时间 = (SELECT MIN(b.访问时间) FROM main.aibase_report_datasource b WHERE b.用户ID = a.用户ID AND b.结果码 > 0);'}, {'role': 'user', 'content': '哪些应用在2025年后成功的平均执行时长高于全局成功平均时长？'}, {'role': 'assistant', 'content': \"SELECT 应用名, COUNT(*) as 访问次数, AVG(执行时长) as 平均时长 FROM main.aibase_report_datasource WHERE 结果码 = 0 AND 访问时间 >= '2025-01-01' GROUP BY 应用名 HAVING AVG(执行时长) > (SELECT AVG(执行时长) FROM main.aibase_report_datasource WHERE 结果码 = 0);\"}, {'role': 'user', 'content': '首页被访问最多的一次是哪一天, 当天一共被访问了多少次?'}]\n", "Using model qwen-max-latest for 1021.75 tokens (approx)\n", "LLM Response: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n", "Extracted SQL: SELECT CAST(\"访问时间\" AS DATE) AS 日期, COUNT(*) AS 访问次数\n", "FROM main.aibase_report_datasource\n", "WHERE \"行为\" = '访问首页'\n", "GROUP BY CAST(\"访问时间\" AS DATE)\n", "ORDER BY 访问次数 DESC\n", "LIMIT 1;\n"]}], "source": []}], "metadata": {"kernelspec": {"display_name": "ai-base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}