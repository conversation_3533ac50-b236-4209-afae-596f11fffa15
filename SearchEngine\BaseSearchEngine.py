import base64
import re
import html2text
from abc import ABC
from typing import Union
from loguru import logger
from playwright.sync_api import Page
from urllib.parse import urlparse, parse_qs, urlencode, urlunparse



class BaseSearchEngine(ABC):
    """
        Playwright
    """
    playwright_page: Page

    clear_html: bool = True

    """
    查询结果页地址, 需要预留{hos_name}填充查询单位
    如 https://www.google.com.hk/
    """
    entry_url: str


    """
    搜索引擎名称
    当前支持: google, baidu, bing, sogou
    """
    search_engine_name: str


    """
    判断是否被ban的元素定位器
    允许是一个列表, 会依次判断
    """
    ban_locator: Union[str, list[str]]

    """
    被屏蔽的搜索引擎
    """
    baned: list[str] = []

    """
    统计总数的正则表达式
    如: 
        文本: 找到约 1,000 条结果
        规则: 找到约 (.*?) 条结果
        结果: 1000
    """
    search_count_regex: str

    """
    搜索内容的正则表达式
    """
    clear_body_regex: str

    page_size: int = 20


    def __init__(
         self,
         playwright_page: Page,
         clear_html: bool = True,
         page_size:int = 20
    ):
        self.clear_html = clear_html
        self.page_size = page_size
        self.playwright_page = playwright_page

        self.initialize()
        logger.success(f'{self.search_engine_name} 搜索引擎初始化成功')



    def initialize(self):
        # self.playwright_page.goto(self.entry_url)
        self.playwright_page.wait_for_load_state('domcontentloaded')


    def crawl(self, search_keyword: str):
        # 跳转到搜索页
        response = self.go_search(search_keyword)

        # 等待加载完成
        self.page_load_wait()

        # ban 检查
        self.is_banned()

        if self.clear_html:
            self.clean_html()

        return response

    def get_text(self, selector: str) -> str:
        return self.playwright_page.inner_text(selector)

    def get_html(self, selector: str) -> str:
        return self.playwright_page.inner_html(selector)

    def get_body_text(self) -> str:
        return self.get_text('body')

    def get_body_html(self) -> str:
        return self.get_html('body')

    def _get_body_text(self) -> str:
        body_text = self.get_body_text()
        if hasattr(self, 'clear_body_regex') and self.clear_body_regex:
            body_text = re.sub(f'{self.clear_body_regex}', '', body_text, flags=re.S)

        return body_text

    def get_search_count(self) -> int:
        body_inner_text = self.get_body_text()
        search_count = 0
        if self.search_count_regex is not None:
            search_count = re.findall(self.search_count_regex, body_inner_text)
            if search_count :
                search_count = int(search_count[0].replace(',', ''))

        return search_count

    def is_banned(self):
        # if self.search_engine_name in self.baned:
        #     raise Exception(f'检测到数据抓取可能被屏蔽 {self.search_engine_name} 已在被屏蔽列表中')

        if isinstance(self.ban_locator, str):
            self.ban_locator = [self.ban_locator]

        for locator in self.ban_locator:
            element = self.playwright_page.locator(locator)
            if element.is_visible():
                continue
            else:
                self.baned.append(self.search_engine_name)
                raise Exception(f'检测到数据抓取可能被屏蔽, {self.search_engine_name} 不满足检测规则: {locator}')

        return False

    def page_load_wait(self):
        pass

    def gen_goto_url(self, hos_name: str) -> str:
        return self.entry_url.format(hos_name=hos_name)

    def go_search(self, search_keyword: str) -> None:
        pass

    def clean_html(self):
        pass

    def get_markdown(self, html:str) -> str:
        text_maker = html2text.HTML2Text()
        text_maker.linebreaks = False

        text_maker.body_width = 0
        # text_maker.ignore_links = True
        text_maker.ignore_images = True
        #text_maker.emphasis_mark = '
        text = text_maker.handle(html)
        return text

    def get_body_markdown(self):
        return self.get_markdown(self.get_body_html())

    def build_url_with_query(self, url: str, update_query_params: dict) -> str:
        parsed_url = urlparse(url)
        cur_query_params = parse_qs(parsed_url.query)

        query_params = {**cur_query_params, **update_query_params}
        new_query_string = urlencode(query_params, doseq=True)

        new_url = urlunparse((parsed_url.scheme, parsed_url.netloc, parsed_url.path, parsed_url.params, new_query_string, parsed_url.fragment))

        return new_url

    def block_css_and_js(self, route, request):
        if request.resource_type == "stylesheet" or request.resource_type == "script":
            route.abort()
        else:
            route.continue_()


    def voucher_screenshot(self):
        screenshot_base64 = base64.b64encode(self.playwright_page.locator('body').screenshot()).decode('utf-8')
        return screenshot_base64


    def __exit__(self, exc_type, exc_val, exc_tb):
        self.playwright_page.close()
        logger.warning(f'{self.search_engine_name} 引擎已关闭, 执行 self.playwright_page.close()')
        return True