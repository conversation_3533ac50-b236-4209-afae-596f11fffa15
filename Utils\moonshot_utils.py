from typing import Dict, Any
import json
from openai import OpenAI
from openai.types.chat.chat_completion import Choice

class MoonshotUtils:
    API_KEY = "sk-B2RVGj9eeu6a7c8wEsbERFrzvAJibiKwVsLzewzsa4QhYa0J"
    BASE_URL = "https://api.moonshot.cn/v1"
    MODEL = "moonshot-v1-128k"

    @staticmethod
    def search_impl(arguments: Dict[str, Any]) -> Any:
        """
        在使用 Moonshot AI 提供的 search 工具的场合，只需要原封不动返回 arguments 即可，
        不需要额外的处理逻辑。
    
        但如果你想使用其他模型，并保留联网搜索的功能，那你只需要修改这里的实现（例如调用搜索
        和获取网页内容等），函数签名不变，依然是 work 的。
    
        这最大程度保证了兼容性，允许你在不同的模型间切换，并且不需要对代码有破坏性的修改。
        """
        return arguments

    @staticmethod
    def chat(messages:list, api_key:str='', model:str='') -> Choice:
        """ 
        使用 Moonshot AI 提供的 联网搜索chat 工具的场合
        """
        client = OpenAI(
            api_key = api_key if api_key else MoonshotUtils.API_KEY,
            base_url = MoonshotUtils.BASE_URL
        )
        completion = client.chat.completions.create(
            model = model if model else MoonshotUtils.MODEL,
            messages = messages,
            temperature = 0.3,
            tools=[
                {
                    "type": "builtin_function",
                    "function": {
                        "name": "$web_search",
                    },
                }
            ]
        )
        return completion.choices[0]
    
    @staticmethod
    def chat_with_web_search(keyword:str, api_key:str='', model:str=''):
        """
        联网搜索，keyword需要完整的含有自己的prompt，示例：请求搜索医生张三的信息并以json格式输出
        """
        messages = [
            {"role": "system", "content": "你是网络数据搜索高手，可以搜索网络中公开的数据"},
        ]
    
        # 初始提问
        messages.append({
            "role": "user",
            "content": keyword
        })
    
        finish_reason = None
        while finish_reason is None or finish_reason == "tool_calls":
            choice = MoonshotUtils.chat(messages, api_key, model)
            finish_reason = choice.finish_reason
            if finish_reason == "tool_calls":  # <-- 判断当前返回内容是否包含 tool_calls
                messages.append(choice.message)  # <-- 我们将 Kimi 大模型返回给我们的 assistant 消息也添加到上下文中，以便于下次请求时 Kimi 大模型能理解我们的诉求
                for tool_call in choice.message.tool_calls:  # <-- tool_calls 可能是多个，因此我们使用循环逐个执行
                    tool_call_name = tool_call.function.name
                    tool_call_arguments = json.loads(tool_call.function.arguments)  # <-- arguments 是序列化后的 JSON Object，我们需要使用 json.loads 反序列化一下
                    if tool_call_name == "$web_search":
                        tool_result = MoonshotUtils.search_impl(tool_call_arguments)
                    else:
                        tool_result = f"Error: unable to find tool by name '{tool_call_name}'"
    
                    # 使用函数执行结果构造一个 role=tool 的 message，以此来向模型展示工具调用的结果；
                    # 注意，我们需要在 message 中提供 tool_call_id 和 name 字段，以便 Kimi 大模型
                    # 能正确匹配到对应的 tool_call。
                    messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": tool_call_name,
                        "content": json.dumps(tool_result),  # <-- 我们约定使用字符串格式向 Kimi 大模型提交工具调用结果，因此在这里使用 json.dumps 将执行结果序列化成字符串
                    })
    
        return choice.message.content  # <-- 在这里，我们才将模型生成的回复返回给用户
    
    @staticmethod
    def search_hcp(keyword:str, api_key:str='', model:str=''):
        """
        联网搜索医生
        """
        keyword = "请搜索医生 "+keyword+"""，并根据搜索到的结果提取医院名称、医生名称、科室名称、性别、职称、职务、类型、专长等信息，
        其中最重要的是医院名称、医生名称、科室名称，
        其次是性别、职称，最后是职务、类型(医师、护师、药剂师、技师等)、专长，
        请以JSON字符串格式输出"""
        return MoonshotUtils.chat_with_web_search(keyword, api_key, model)
    