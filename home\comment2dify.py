import time
import random
import requests
import pandas as pd
from tqdm import tqdm
from sqlalchemy import create_engine

engine = create_engine(f"postgresql+psycopg2://postgres:qBTpje8V@192.168.16.150:54154/demo?client_encoding=utf8")
dataset_id = 'f4ca988b-ef44-429d-bcd0-55029d89450c'
document_id = '377d7cfe-549d-4adb-90d0-035d331fede7'
api_key = 'dataset-vgIe7WdBYujgNfHWOmmRk0yM'
api_url = f'https://ai-base.medsci.cn/v1/datasets/{dataset_id}/documents/{document_id}/segments'
headers = {
    'Authorization': f'Bearer {api_key}',
    'Content-Type': 'application/json; charset=utf-8',
    'User-Agent': 'HOME COMMENT IMPORTER/1.0'
}

def main():
    # 获取数据
    query = """
        select
            CONCAT('标题: ', mg_title, '\n提问: ', mg_content, '\n回答: ', reply_content) comment_text
        from home.主站留言数据
        where 是否入库 = 'Y';
    """
    df = pd.read_sql_query(query, con=engine)
    if not df.empty:
        # 遍历数据并发送请求
        for _, row in tqdm(df.iterrows(), total=len(df), desc="Processing segments"):
            try:
                response = requests.post(
                    api_url,
                    headers=headers,
                    json={"segments": [{"content": row['comment_text']}]}
                )
                response.raise_for_status()
            except Exception as e:
                print(f"Error sending request for segment: {e}, {row}")

            # 随机加入一个暂停, 避免API 限流
            time.sleep(random.uniform(0.1, 0.9))

if __name__ == "__main__":
    main()