import streamlit as st
from auth import verify_token
from vanna_calls import (
    avatar_url,
    show_examples,
    st_page_init,
    env_vals,
    on_question
)
# """
# 运行命令
# > cd vannaReport/
# > streamlit run app.py 
# """
st.set_page_config(
    page_title="梅斯小智用户访问行为交互平台",
    layout="centered",
    initial_sidebar_state=env_vals.get('ST_INITIAL_SIDEBAR_STATE', 'collapsed')
)

result = verify_token()
if not result["valid"]:
    st.error(result["error"])
    st.stop()

# 检查是否需要清除会话并重新运行
if st.session_state.get("_needs_rerun", False):
    del st.session_state["_needs_rerun"]
    st.rerun()

st_page_init()
question_input = st.chat_input("关于用户的访问行为, 您想了解些什么? 请向我提问")

if question_input:
    on_question(question_input)

if not question_input:
    assistant_message_suggested = st.chat_message("assistant", avatar=avatar_url)
    if assistant_message_suggested.button("点击显示提问示列"):
        show_examples()
