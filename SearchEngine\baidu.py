from loguru import logger
from SearchEngine.BaseSearchEngine import BaseSearchEngine


class BaiduSearchEngine(BaseSearchEngine):
    search_engine_name = 'baidu'

    entry_url = 'https://www.baidu.com/'


    ban_locator = 'input[id="kw"]'

    def initialize(self):
        super().initialize()

        self.playwright_page.route('**/*.css', self.block_css_and_js)

        cookies = self.playwright_page.context.cookies()
        for cookie in cookies:
            if cookie['name'] == 'BAIDUID':
                self.playwright_page.context.add_cookies([
                    {
                        "name": "BAIDUID",
                        "value": f"{cookie['value']}:NR={self.page_size}",
                        "domain": cookie['domain'],
                        "path": cookie["path"],
                        "expires": cookie['expires'],
                        "httpOnly": cookie["httpOnly"],
                        "secure": cookie["secure"],
                        "sameSite": cookie['sameSite'],
                    }
                ])

        logger.success(f'{self.search_engine_name} initialize success!')

    def go_search(self, search_keyword: str):
        cur_url = self.playwright_page.url
        if 'www.baidu.com/s?wd=' in cur_url:
            go_search_url = self.build_url_with_query(self.playwright_page.url, {'wd': search_keyword})
        else:
            go_search_url = f'https://www.baidu.com/s?wd={search_keyword}'

        return self.playwright_page.goto(go_search_url)

    def page_load_wait(self):
        self.playwright_page.wait_for_load_state('networkidle')
        self.playwright_page.wait_for_load_state('domcontentloaded')

    def clean_html(self):
        self.playwright_page.add_script_tag(path='resource/jquery-3.7.1.min.js')

        # 移除无用dom
        self.playwright_page.evaluate_handle("""
            $('video').remove();
            $('script').remove();
            $('style').remove();
            $('img').remove();
            $('svg').remove();
            $('div.tts').remove();
            $('div.c-tools').remove();
            $('#s_is_result_css').remove();
            $('#s_index_off_css').remove();
            $('#css_result').remove();
            $('#aging-total-page').remove();
            $('div#head').remove();
            $('div#s_tab').remove();
            $('div#tsn_inner').remove();
            $('div#rs_new').remove();
            $('div#content_right').remove();
            $('div#page').remove();
            $('div#foot').remove();
            
            $('div.search-quit-dialog-wrap').remove();
            $('div[tpl=ask_doctor]').remove();
            $('div[tpl=recommend_list]').remove();
            $('div[tpl="app/rs"]').remove();
            $('div[tpl="short_video"]').remove();
            $('div[tpl="med_wenzhen_san"]').remove();
            $('div.c-img').parent().parent().remove();
            $('div#result_tts_player').remove();
            $('div.hit-toptip').remove();

            $('*').removeAttr('style');
        """)

        div_mus_elements = self.playwright_page.locator('xpath=//div[@id="content_left"]/div[@mu]').all()
        for div_mu_element in div_mus_elements:
            attr_mu = div_mu_element.get_attribute('mu')

            a_elements = div_mu_element.locator('xpath=//a').all()
            for a_element in a_elements:
                a_element.evaluate(f'element => element.setAttribute("href", "{attr_mu}")')
            #div_mu_element.evaluate('element => element.innerHTML += "<div>_____</div"')

        # logger.debug(results_list)

    def format_result(self, data_format: str = 'json'):

        def parse_items():
            return self.playwright_page.locator('xpath=//div[@id="content_left"]/div[@mu]').all()

        def parse_url(item):
            return item.get_attribute('mu')

        def parse_title(item):
            inner_text = ''
            try:
                inner_text = item.locator('xpath=//h3').inner_text(timeout=100)
            except Exception as e:
                inner_text = ''
                logger.warning(f'parse_title error: {e}')
            finally:
                return inner_text

        def parse_source(item):
            inner_text = ''
            try:
                inner_text = item.locator('xpath=//div[contains(@class, "source")]').first.inner_text(timeout=100)
            except Exception as e:
                inner_text = ''
                logger.warning(f'parse_source error: {e}')
            finally:
                return inner_text

        def parse_description(item):
            inner_text = ''
            try:
                description_locator = item.locator('xpath=//div[contains(@class, "content")]').all()
                if len(description_locator) > 0:
                    description_locator = description_locator[0]
                else:
                    description_locator = item.locator('xpath=//span[contains(@class, "content-right")]')

                inner_text = description_locator.inner_text(timeout=100)
            except Exception as e:
                inner_text = ''
                logger.warning(f'parse_description error: {e}')
            finally:
                return inner_text


        results_list = []
        div_mus_elements = parse_items()
        for div_mu_element in div_mus_elements:
            if data_format == 'json':
                result_item = {
                    #'url': parse_url(div_mu_element),
                    #'title': parse_title(div_mu_element),
                    #'source': parse_source(div_mu_element),
                    'description': parse_description(div_mu_element),
                }
                results_list.append(result_item)

            else:
                results_list.append(self.get_markdown(div_mu_element.inner_html()))

        return results_list
