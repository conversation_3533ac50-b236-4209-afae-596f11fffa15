import os
import cv2
import tempfile
from ultralytics import YOLO
from qcloud_cos import CosConfig, CosS3Client

yolo10 = YOLO("yolo/weights/v0620.pt")

cos_client = CosS3Client(CosConfig(
    Region=os.getenv('API_KEY'),
    SecretId=os.getenv('COS_SECRET_ID'),
    SecretKey=os.getenv('COS_SECRETKEY'),
    Scheme='https')
)


def yolo_detect(file, debug=0):
    results = yolo10([file], device='cpu')
    predict_results = []
    url = None
    for result in results:
        img = result.orig_img

        # Iterate through detected boxes
        for box in result.boxes:
            x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())
            confidence = box.conf[0].item()

            # Draw the bounding box with a smaller thickness
            cv2.rectangle(img, (x1, y1), (x2, y2), (0, 0, 255), 1)
            predict_results.append([(x1, y1), (x2, y2)])
            # Add label with confidence
            cv2.putText(img, f'{confidence:.2f}', (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            cv2.putText(img, f'LT: {(x1, y1)}', (x1, y2 +10), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)
            cv2.putText(img, f'RB: {(x2, y2)}', (x1, y2 +23), cv2.FONT_HERSHEY_SIMPLEX, 0.3, (0, 0, 255), 1)

        if debug:
            with tempfile.NamedTemporaryFile(delete=True, suffix='.png') as predict_temp_file:
                cv2.imwrite(predict_temp_file.name, img)
                predict_temp_file.flush()
                cos_client.upload_file(
                    Bucket=os.getenv('COS_BUCKET'),
                    Key=f'MRBASE-COS-FILE/{predict_temp_file.name}',
                    LocalFilePath=predict_temp_file.name,
                    ContentType= 'image/png',
                    ContentDisposition='inline',
                )
                url = cos_client.get_object_url(Bucket=os.getenv('COS_BUCKET'), Key=f'MRBASE-COS-FILE/{predict_temp_file.name}')

    return url, predict_results

