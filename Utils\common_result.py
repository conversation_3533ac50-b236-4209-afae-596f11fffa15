from pydantic import BaseModel, Field
from typing import Optional
from typing import Union

class CommonResult(BaseModel):
    code: int = Field(0, description="响应码")
    status: int = Field(200, description="响应码")
    message: Optional[str] = Field("操作成功", description="响应信息")
    data: Union[dict, list, str, int, None] = Field(None, description="响应数据")

    @staticmethod
    def success(data: Union[dict, list, None] = None, message: str = "操作成功") -> "CommonResult":
        return CommonResult(code=0, status=200, message=message, data=data)
    
    @staticmethod
    def error(message: str = "操作失败", data: dict|list = None) -> "CommonResult":
        return CommonResult(code=500, status=500, message=message, data=data)