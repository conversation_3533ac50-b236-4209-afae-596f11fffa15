import time
import duckdb
from loguru import logger
import streamlit as st
from vanna.qianwen.QianwenAI_chat import QianWenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from dotenv import dotenv_values

avatar_url = '<svg t="1741090240823" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1550" width="200" height="200"><path d="M717.12 274H762c82.842 0 150 67.158 150 150v200c0 82.842-67.158 150-150 150H262c-82.842 0-150-67.158-150-150V424c0-82.842 67.158-150 150-150h44.88l-18.268-109.602c-4.086-24.514 12.476-47.7 36.99-51.786 24.514-4.086 47.7 12.476 51.786 36.99l20 120c0.246 1.472 0.416 2.94 0.516 4.398h228.192c0.1-1.46 0.27-2.926 0.516-4.398l20-120c4.086-24.514 27.272-41.076 51.786-36.99 24.514 4.086 41.076 27.272 36.99 51.786L717.12 274zM262 364c-33.138 0-60 26.862-60 60v200c0 33.138 26.862 60 60 60h500c33.138 0 60-26.862 60-60V424c0-33.138-26.862-60-60-60H262z m50 548c-24.852 0-45-20.148-45-45S287.148 822 312 822h400c24.852 0 45 20.148 45 45S736.852 912 712 912H312z m-4-428c0-24.852 20.148-45 45-45S398 459.148 398 484v40c0 24.852-20.148 45-45 45S308 548.852 308 524v-40z m318 0c0-24.852 20.148-45 45-45S716 459.148 716 484v40c0 24.852-20.148 45-45 45S626 548.852 626 524v-40z" fill="#1296db" p-id="1551"></path></svg>'


class MyVanna(ChromaDB_VectorStore, QianWenAI_Chat):
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        QianWenAI_Chat.__init__(self, config=config)

env_vals = dotenv_values()
logger.debug(env_vals)

my_vn = MyVanna(config={'api_key': env_vals.get('LLM_API_KEY'), 'model': env_vals.get('LLM_MODEL')})
my_vn.connect_to_duckdb(url='file:ai-base-report-datasource.duckdb')
ST_CACHE_DATA_TTL = env_vals.get('ST_CACHE_DATA_TTL', 3600)


def st_page_init():
    st.title("梅斯小智用户访问行为交互平台")
    st.sidebar.markdown(
        """
        <div style="text-align: center;margin-bottom:20px">
            <img src="https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png" style="max-width: 200px;">
        </div>
        """,
        unsafe_allow_html=True
    )
    st.sidebar.title("输出设置")
    st.sidebar.checkbox("显示SQL", value=True, key="show_sql")
    st.sidebar.checkbox("显示表格", value=True, key="show_table")
    st.sidebar.checkbox("显示Plotly代码", value=False, key="show_plotly_code")
    st.sidebar.checkbox("显示图表", value=False, key="show_chart")
    st.sidebar.checkbox("显示概括", value=False, key="show_summary")
    st.sidebar.checkbox("显示追问", value=False, key="show_followup")
    st.sidebar.checkbox("独立提问", value=True, key="context", help='启用此项每一个提问都是单独会话, 不带上下文')
    st.sidebar.button("新会话", on_click=lambda: set_new_session(), use_container_width=True)
    st.sidebar.button("更新数据源", on_click=lambda: on_update_data_source(), use_container_width=True, type="primary")

@st.cache_resource(ttl=ST_CACHE_DATA_TTL)
def setup_vanna():
    return my_vn

@st.cache_data(show_spinner="正在生成示列提问...", ttl=ST_CACHE_DATA_TTL)
def generate_questions_cached():
    vn = setup_vanna()
    return vn.generate_questions()

@st.cache_data(show_spinner="正在生成SQL...", ttl=ST_CACHE_DATA_TTL)
def generate_sql_cached(question: str):
    vn = setup_vanna()
    return vn.generate_sql(question=question, allow_llm_to_see_data=True)

@st.cache_data(show_spinner="正在检查SQL...", ttl=ST_CACHE_DATA_TTL)
def is_sql_valid_cached(sql: str):
    vn = setup_vanna()
    return vn.is_sql_valid(sql=sql)

@st.cache_data(show_spinner="正在运行SQL查询...", ttl=ST_CACHE_DATA_TTL)
def run_sql_cached(sql: str):
    vn = setup_vanna()
    result = vn.run_sql(sql=sql)
    logger.debug(result)
    return result

@st.cache_data(show_spinner="正在分析是否应该生成图表...", ttl=ST_CACHE_DATA_TTL)
def should_generate_chart_cached(question, sql, df):
    vn = setup_vanna()
    return vn.should_generate_chart(df=df)

@st.cache_data(show_spinner="正在生成图表代码 ...", ttl=ST_CACHE_DATA_TTL)
def generate_plotly_code_cached(question, sql, df):
    vn = setup_vanna()
    code = vn.generate_plotly_code(question=question, sql=sql, df=df)
    return code

@st.cache_data(show_spinner="正在运行图表代码...", ttl=ST_CACHE_DATA_TTL)
def generate_plot_cached(code, df):
    vn = setup_vanna()
    return vn.get_plotly_figure(plotly_code=code, df=df)

@st.cache_data(show_spinner="正在生成追问...", ttl=ST_CACHE_DATA_TTL)
def generate_followup_cached(question, sql, df):
    vn = setup_vanna()
    return vn.generate_followup_questions(question=question, sql=sql, df=df)

@st.cache_data(show_spinner="正在生成SQL说明...", ttl=ST_CACHE_DATA_TTL)
def generate_summary_cached(question, df):
    vn = setup_vanna()
    return vn.generate_summary(question=question, df=df)

def update_data_source_cached():
    with st.spinner("正在更新数据源..."):
        st.cache_resource.clear()

        pg_conn = env_vals.get('PG_CONN')
        conn = duckdb.connect('ai-base-report-datasource.duckdb')
        conn.execute("INSTALL postgres;")
        conn.execute("LOAD postgres;")
        try:
            # 配置数据库账号
            conn.execute(f"""
                    ATTACH '{pg_conn}'
                    AS pg_db (TYPE postgres);
                """)
            logger.success("数据库附加成功")
        except duckdb.BinderException as e:
            if "database with name" in str(e) and "already exists" in str(e):
                logger.warning("数据库已存在，无需重复附加")
            else:
                raise  # 如果是其他类型的BinderException，则重新抛出异常
        except Exception as e:
            raise

        # 查询 PostgreSQL 数据
        result = conn.execute("SELECT version(), count(1)  FROM pg_db.ai_base.ai_app_langs").fetchone()
        logger.info('测试数据库连接状态: ', result)

        # 将数据从pgsql导入到duckdb
        conn.execute("DROP TABLE IF EXISTS main.aibase_report_datasource;")
        conn.execute("""
            CREATE TABLE main.aibase_report_datasource AS
            select
                CASE when log.行为 = '访问首页' then '访问首页'
                    else app.app_name
                end as 应用名,
                log.*
            from (
                 select
                     user_id 用户ID,
                     CASE user_type
                         WHEN 37 THEN 'MedSci xAI'
                         WHEN 0 THEN '主站'
                         WHEN 36 THEN 'Facebook'
                         WHEN 35 THEN 'Google'
                         ELSE CAST(user_type AS TEXT)
                         END AS 用户类型,
                     case request_url
                         WHEN '/ai-base/index/getAppTypes' THEN '访问首页'
                         ELSE '聊天互动'
                         END AS 行为,
                     ((request_params::json)->>'body')::json->>'appUuid' AS app_uuid,
                     CASE
                         WHEN user_agent ILIKE '%Mobile%'
                             OR user_agent ILIKE '%Android%'
                             OR user_agent ILIKE '%iPhone%'
                             OR user_agent ILIKE '%iPad%'
                             OR user_agent ILIKE '%Windows Phone%'
                             THEN '移动端'
                         ELSE 'PC端'
                     END AS 设备类型,
                     duration 执行时长,
                     result_code 结果码,
                     create_time 访问时间
                 from pg_db.yudao.infra_api_access_log log
                 where request_url in ('/ai-base/index/getAppTypes', '/ai-base/chat/chat-messages', '/ai-base/chat/workflows/run', '/ai-base/chat/completion-messages',
                     '/ai-base/yps-chat/chat-messages', '/ai-base/yps-chat/completion-messages', '/ai-base/yps-chat/workflows/run1')
            )log
            left join pg_db.ai_base.ai_app_langs app on log.app_uuid <> '' and app.app_uuid::text = log.app_uuid;
        """)
        conn.commit()

        # 测速数据是否导入成功
        import_test = conn.execute("select count(1), min(访问时间), max(访问时间), count(DISTINCT 用户ID) from main.aibase_report_datasource limit 3").fetchdf()
        logger.info(f'测试数据库导入状态: {import_test=}')

        return {"updated_at": time.time()}
def on_question(question):

    question_prompt = gen_question(question)
    set_last_question(question)
    st.sidebar.write(st.session_state)

    user_message = st.chat_message("user")
    user_message.write(question)

    # text to sql
    sql = generate_sql_cached(question=question_prompt)
    if not is_sql_valid_cached(sql=sql):
        assistant_message = st.chat_message("assistant", avatar=avatar_url)
        assistant_message.write(sql)
        assistant_message.error("无法为该问题生成有效SQL, 如系统判断有误请截图反馈给管理员!")
        st.stop()

    show_sql_by_switch(sql)

    df = run_sql_cached(sql=sql)
    if df.empty:
        assistant_message = st.chat_message("assistant", avatar=avatar_url)
        assistant_message.write(sql)
        assistant_message.warning("提问生成的SQL无法查询到数据, 如数据库中存在满足条件的数据, 请截图反馈给管理员!")
        st.stop()

    show_table_by_switch(df)

    show_chart_by_switch(question_prompt, sql, df)

    show_summary_by_switch(question_prompt, df)

    show_followup_by_switch(question_prompt, sql, df)

def set_last_question(question, context_len = 2):
    last_question_list = st.session_state.get("last_question", [])
    st.session_state["last_question"] = (last_question_list+[question])[-context_len:]
    logger.debug(f'正在执行 set_last_question: {question}')
    logger.debug(st.session_state)

def gen_question(question):
    last_question_list = st.session_state.get("last_question", [])
    if st.session_state.get("context", False) and last_question_list:
        last_question = '\n'.join(last_question_list)
        question = f"""
            本次提问请结合用户历史提问上下文信息进行改写!
            #历史提问#: {last_question}

            #本次提问#: {question}
        """
    return question

def set_new_session():
    st.session_state["_needs_rerun"] = True

def show_auto_dismissible_message(message, message_type="info", duration_secs=5):
    """显示一个会自动消失的消息"""
    message_container = st.empty()

    if message_type == "success":
        message_container.success(message)
    elif message_type == "error":
        message_container.error(message)
    else:
        message_container.info(message)

    time.sleep(duration_secs)
    message_container.empty()
    return message_container

def on_update_data_source():
    # 使用 session_state 来存储变量, 页面刷新后失效
    if 'last_update_time' not in st.session_state:
        st.session_state.last_update_time = 0  # 上次更新的时间戳

    # 计算距离上次更新是否已经超过1小时
    current_time = time.time()
    time_since_last_update = current_time - st.session_state.last_update_time

    # 更新数据按钮
    if time_since_last_update > 3600 or st.session_state.last_update_time == 0:
        # 更新数据
        data = update_data_source_cached()
        st.session_state.last_update_time = current_time
        show_auto_dismissible_message("数据源更新成功！", "success")

    else:
        if time_since_last_update <= 3600 and st.session_state.last_update_time != 0:
            remaining_time = int(3600 - time_since_last_update)
            remaining_minutes = max(1, remaining_time // 60)  # 将秒转换为分钟，小于60秒显示为1分钟
            show_auto_dismissible_message(f"距离上次更新还未超过1小时，请稍后再试。（剩余: {remaining_minutes}分钟）")

def show_examples():
    questions = generate_questions_cached()
    for i, question in enumerate(questions):
        time.sleep(0.05)
        st.button(
            question,
            on_click=on_question,
            args=(question,),
        )

def show_sql_by_switch(sql):
    if st.session_state.get("show_sql", True):
        assistant_message_sql = st.chat_message("assistant", avatar=avatar_url)
        assistant_message_sql.code(sql, language="sql", line_numbers=True)


def show_table_by_switch(df):
    if st.session_state.get("show_table", True):
        assistant_message_table = st.chat_message("assistant", avatar=avatar_url, )
        df_limit = 20
        logger.debug(f'正在执行 show_table_by_switch: {df=}')

        # 处理日期列，移除时分秒
        df_display = df.copy()
        for col in df_display.select_dtypes(include=['datetime64']).columns:
            df_display[col] = df_display[col].dt.date

        if len(df_display) > df_limit:
            assistant_message_table.text(f"仅显示前{df_limit}条数据")
            assistant_message_table.dataframe(df_display.head(df_limit))
        else:
            assistant_message_table.dataframe(df_display)

def show_summary_by_switch(my_question, df):
    if st.session_state.get("show_summary", True):
        assistant_message_summary = st.chat_message("assistant", avatar=avatar_url)
        summary = generate_summary_cached(question=my_question, df=df)
        if summary is not None:
            assistant_message_summary.text(summary)

def show_followup_by_switch(my_question, sql, df):
    if st.session_state.get("show_followup", True):
        assistant_message_followup = st.chat_message("assistant", avatar=avatar_url)
        followup_questions = generate_followup_cached(
            question=my_question, sql=sql, df=df
        )
        #st.session_state["df"] = None

        if len(followup_questions) > 0:
            assistant_message_followup.text("继续追问?")
            for question in followup_questions[:5]:
                assistant_message_followup.button(question, on_click=on_question, args=(question,))

def show_chart_by_switch(my_question, sql, df):
    if should_generate_chart_cached(question=my_question, sql=sql, df=df):
        code = generate_plotly_code_cached(question=my_question, sql=sql, df=df)

        if st.session_state.get("show_plotly_code", False):
            assistant_message_plotly_code = st.chat_message("assistant", avatar=avatar_url)
            assistant_message_plotly_code.code(code, language="python", line_numbers=True)

        if code is not None and code != "":
            if st.session_state.get("show_chart", True):
                assistant_message_chart = st.chat_message("assistant", avatar=avatar_url)
                fig = generate_plot_cached(code=code, df=df)
                if fig is not None:
                    assistant_message_chart.plotly_chart(fig)
                else:
                    assistant_message_chart.error("此问题无法生成图表, 请截图反馈给管理员")
