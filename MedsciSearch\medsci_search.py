# -*- coding: utf-8 -*-
"""
MedSci智能搜索服务模块

本模块提供基于Milvus向量数据库的智能搜索功能，支持：
- 语义搜索：基于向量相似度的智能检索
- 全文检索：基于BM25算法的关键词匹配
- 智能切换：根据查询内容自动选择最佳检索方式
- 时间权重：根据发布时间调整搜索结果排序
- 多种筛选：支持时间范围、内容类型等筛选条件

Author: MedSci Team
Version: 2.0
Date: 2024
"""

import time
import re
import requests
import os
import threading
import json
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from pymilvus import connections, Collection, utility
from dotenv import load_dotenv
from Utils.common_result import CommonResult
from dataclasses import dataclass
from datetime import datetime, timedelta

# 加载环境变量
load_dotenv()

@dataclass
class FailedKeyRecord:
    """失效密钥记录"""
    api_key: str
    error_type: str  # '429' 或 '401'
    failed_time: float  # 失效时间戳
    reset_after_seconds: int  # 重置等待时间（秒）
    
    def is_expired(self) -> bool:
        """检查是否已过期（可以重置）"""
        return time.time() - self.failed_time >= self.reset_after_seconds

class MultiApiKeyManager:
    """
    多API密钥管理器
    
    功能：
    - 管理多个API密钥
    - 实现轮询负载均衡
    - 自动处理失效密钥
    - 支持基于错误类型的差异化重置时间
    - 线程安全
    """
    
    # 重置时间配置（秒）
    RESET_TIMES = {
        '429': 10,      # 速率限制：10秒后重置
        '401': 7200,    # 认证失败：2小时后重置
        'default': 300  # 其他错误：5分钟后重置
    }
    
    def __init__(self):
        # 从环境变量获取API密钥数组
        self.api_keys = self._load_api_keys()
        self.current_index = 0
        self.failed_records: Dict[str, FailedKeyRecord] = {}  # 失效记录字典
        self.lock = threading.Lock()  # 线程锁
        
    def _load_api_keys(self) -> List[str]:
        """
        加载API密钥列表
        
        Returns:
            List[str]: API密钥列表
        """
        # 直接在代码中定义API密钥列表
        api_keys = [
            "sk-nznfahyzsbzmkdgslmpzxpdlwdmeiyhrzuybeytlrhrcabww",
            "sk-cdxdmldzqzzkaqruauqxvfuyjlpricqgrjrulmftijopnydg",
            "sk-qyxnqveidzufmbajraxryttfgzarcqbjkkzzjxpblfrqompk",
            "sk-pzsuinkblrvtdgqdwvurujwnwisqvuyoxzxtuypkntazwvsz",
            "sk-vawombwmwinveeurrftmdiuegndsdrwmttolrahkrafhmlxz",
            "sk-zeytfobbybsjliwnkerucuecimfebbnwqyvjbjsrbvrnzmwv",
            "sk-ysppfbputxeyqbaxtjlgcywlnkqqgmdbvisstjxarrxwdqrh",
            "sk-xymlgwotcyezmtuiumtqhxbpycicojgersobjggoxkoxoecc"
        ]
        
        if not api_keys:
            raise ValueError("API密钥列表不能为空")
        
        # 验证每个密钥都是字符串且非空
        for key in api_keys:
            if not isinstance(key, str) or not key.strip():
                raise ValueError("API密钥必须是非空字符串")
        
        return api_keys
    
    def _cleanup_expired_records(self):
        """
        清理已过期的失效记录（内部方法，调用时需要已获得锁）
        """
        expired_keys = []
        for api_key, record in self.failed_records.items():
            if record.is_expired():
                expired_keys.append(api_key)
        
        for key in expired_keys:
            del self.failed_records[key]
    
    def get_next_api_key(self) -> Optional[str]:
        """
        获取下一个可用的API密钥（轮询方式）
        
        Returns:
            Optional[str]: 可用的API密钥，如果没有可用密钥返回None
        """
        with self.lock:
            if not self.api_keys:
                return None
            
            # 清理过期的失效记录
            self._cleanup_expired_records()
            
            # 获取可用的密钥列表（排除仍在失效期内的密钥）
            available_keys = [key for key in self.api_keys if key not in self.failed_records]
            
            if not available_keys:
                return None
            
            # 轮询选择密钥
            key = available_keys[self.current_index % len(available_keys)]
            self.current_index = (self.current_index + 1) % len(available_keys)
            
            return key
    
    def mark_key_failed(self, api_key: str, error_type: str = 'default'):
        """
        标记API密钥为失效状态
        
        Args:
            api_key (str): 失效的API密钥
            error_type (str): 错误类型 ('429', '401', 'default')
        """
        with self.lock:
            reset_time = self.RESET_TIMES.get(error_type, self.RESET_TIMES['default'])
            
            record = FailedKeyRecord(
                api_key=api_key,
                error_type=error_type,
                failed_time=time.time(),
                reset_after_seconds=reset_time
            )
            
            self.failed_records[api_key] = record
    
    def get_stats(self) -> dict:
        """
        获取API密钥使用统计
        
        Returns:
            dict: 包含总密钥数、可用密钥数、失效密钥数的统计信息
        """
        with self.lock:
            # 清理过期记录
            self._cleanup_expired_records()
            
            total_keys = len(self.api_keys)
            failed_count = len(self.failed_records)
            available_count = total_keys - failed_count
            
            # 按错误类型分组统计
            error_stats = {}
            for record in self.failed_records.values():
                error_type = record.error_type
                if error_type not in error_stats:
                    error_stats[error_type] = 0
                error_stats[error_type] += 1
            
            return {
                'total_keys': total_keys,
                'available_keys': available_count,
                'failed_keys': failed_count,
                'failed_records': {
                    key: {
                        'error_type': record.error_type,
                        'failed_time': record.failed_time,
                        'reset_after_seconds': record.reset_after_seconds,
                        'remaining_seconds': max(0, record.reset_after_seconds - (time.time() - record.failed_time))
                    } for key, record in self.failed_records.items()
                },
                'error_type_stats': error_stats
            }

class MilvusConnectionManager:
    """
    Milvus连接管理器 - 单例模式
    
    解决每次搜索都重新连接数据库的性能问题。
    使用单例模式管理连接，避免频繁的连接和断开操作。
    """
    
    _instance = None
    _connection = None
    _collection = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MilvusConnectionManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        self.MILVUS_HOST = os.getenv('MILVUS_HOST', '127.0.0.1')
        self.MILVUS_PORT = os.getenv('MILVUS_PORT', '19530')
        self.MILVUS_DB_NAME = os.getenv('MILVUS_DB_NAME', 'default')
        self.collection_name = "medsci_search"
        self._connected = False
    
    def connect(self) -> bool:
        """
        建立Milvus连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if not self._connected:
                connections.connect(
                    alias="default",
                    host=self.MILVUS_HOST,
                    port=self.MILVUS_PORT,
                    db_name=self.MILVUS_DB_NAME
                )
                self._connected = True
            
            # 检查集合是否存在
            if not utility.has_collection(self.collection_name):
                return False
            
            # 获取集合对象并加载
            if self._collection is None:
                self._collection = Collection(name=self.collection_name)
            
            # 确保集合已加载（兼容不同版本的pymilvus）
            try:
                # 尝试加载集合（如果已加载则不会重复加载）
                self._collection.load()
            except Exception as load_error:
                # 如果加载失败，记录错误但不一定是致命错误
                # 某些情况下集合可能已经加载，这个错误可以忽略
                pass
            
            return True
            
        except Exception as e:
            self._connected = False
            return False
    
    def get_collection(self) -> Optional[Collection]:
        """
        获取集合对象
        
        Returns:
            Optional[Collection]: 集合对象，失败时返回None
        """
        if self.connect():
            return self._collection
        return None
    
    def disconnect(self):
        """
        断开连接（通常在应用关闭时调用）
        """
        try:
            if self._connected:
                connections.disconnect("default")
                self._connected = False
                self._collection = None
        except Exception as e:
            pass

class SearchRequest(BaseModel):
    """
    搜索请求参数模型
    
    设计原则：
    - q参数必填，但不限制类型（支持数字、字符串等）
    - 其他参数都可选，支持空值时使用默认值
    - 移除过于严格的类型和范围限制
    """
    
    # q参数：必填，但不限制类型
    q: Any = Field(
        default=None,
        description="搜索查询内容，支持任意类型（字符串、数字等）",
        example="糖尿病治疗方法",
        title="查询内容"
    )
    
    # module参数：可选，空值时不过滤
    module: Optional[Any] = Field(
        default=None,
        description="检索模块类型，可选参数。如果为空则查询所有数据",
        example="article",
        title="模块类型"
    )
    
    # time_type参数：可选，空值时使用默认值0
    time_type: Optional[Any] = Field(
        default=0,
        description="时间范围筛选，可选参数。空值时默认为0（所有时间）",
        title="时间筛选"
    )
    
    # sort_type参数：可选，空值时使用默认值1
    sort_type: Optional[Any] = Field(
        default=1,
        description="结果排序方式，可选参数。空值时默认为1（按相关度排序）",
        title="排序方式"
    )
    
    # search_type参数：可选，空值时使用默认值1
    search_type: Optional[Any] = Field(
        default=1,
        description="搜索范围，可选参数。空值时默认为1（全文搜索）",
        title="搜索范围"
    )
    
    # limit参数：可选，空值时使用默认值10
    limit: Optional[Any] = Field(
        default=10,
        description="返回结果数量限制，可选参数。空值时默认为10",
        title="结果数量"
    )
    
    @validator('q', pre=True)
    def validate_q(cls, v):
        """验证q参数：必填，但不限制类型"""
        if v is None or str(v).strip() == "":
            return None
        # 转换为字符串用于搜索
        return str(v)
    
    @validator('module', pre=True)
    def validate_module(cls, v):
        """验证module参数：空值时返回None"""
        if v is None or str(v).strip() == "" or str(v).lower() in ["null", "none"]:
            return None
        return str(v)
    
    @validator('time_type', pre=True)
    def validate_time_type(cls, v):
        """验证time_type参数：空值时使用默认值0"""
        if v is None or str(v).strip() == "" or str(v).lower() in ["null", "none"]:
            return 0
        try:
            return int(v)
        except (ValueError, TypeError):
            return 0  # 转换失败时使用默认值
    
    @validator('sort_type', pre=True)
    def validate_sort_type(cls, v):
        """验证sort_type参数：空值时使用默认值1"""
        if v is None or str(v).strip() == "" or str(v).lower() in ["null", "none"]:
            return 1
        try:
            return int(v)
        except (ValueError, TypeError):
            return 1  # 转换失败时使用默认值
    
    @validator('search_type', pre=True)
    def validate_search_type(cls, v):
        """验证search_type参数：空值时使用默认值1"""
        if v is None or str(v).strip() == "" or str(v).lower() in ["null", "none"]:
            return 1
        try:
            return int(v)
        except (ValueError, TypeError):
            return 1  # 转换失败时使用默认值
    
    @validator('limit', pre=True)
    def validate_limit(cls, v):
        """验证limit参数：空值时使用默认值10"""
        if v is None or str(v).strip() == "" or str(v).lower() in ["null", "none"]:
            return 10
        try:
            limit_val = int(v)
            return limit_val
        except (ValueError, TypeError):
            return 10  # 转换失败时使用默认值

class SearchService:
    """
    MedSci智能搜索服务核心类
    
    提供完整的搜索功能，包括：
    - 智能检索方式选择（语义搜索 vs 全文检索）
    - 向量相似度搜索
    - BM25全文检索
    - 时间权重计算
    - 结果排序和筛选
    - 多API密钥负载均衡
    
    Attributes:
        MILVUS_HOST: Milvus向量数据库服务器地址
        MILVUS_PORT: Milvus服务端口
        MILVUS_DB_NAME: 数据库名称
        api_manager: 多API密钥管理器
    """
    
    # Milvus向量数据库连接配置（从环境变量读取）
    MILVUS_HOST = os.getenv('MILVUS_HOST', '127.0.0.1')  # 向量数据库服务器IP
    MILVUS_PORT = os.getenv('MILVUS_PORT', '19530')      # 服务端口
    MILVUS_DB_NAME = os.getenv('MILVUS_DB_NAME', 'default')  # 数据库名称
    
    def should_use_fulltext_search(self, query: str) -> bool:
        """
        智能判断是否应该使用全文检索
        
        根据查询内容的特征自动选择最佳的检索方式：
        - 中文查询：≤4个汉字使用全文检索，>4个汉字使用语义搜索
        - 英文查询：<3个单词使用全文检索，≥3个单词使用语义搜索
        - 特殊情况：纯数字、符号、过短查询强制使用全文检索
        
        Args:
            query (str): 用户输入的搜索查询字符串
            
        Returns:
            bool: True表示使用全文检索，False表示使用语义搜索
            
        Examples:
            >>> service.should_use_fulltext_search("糖尿病")  # 3个汉字
            True
            >>> service.should_use_fulltext_search("糖尿病的治疗方法研究")  # 8个汉字
            False
            >>> service.should_use_fulltext_search("COVID-19")  # 特殊符号
            True
        """
        if not query or not query.strip():
            return True
            
        query = query.strip()
        
        # 统计中文字符数
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', query)
        chinese_count = len(chinese_chars)
        
        # 统计英文单词数
        english_words = re.findall(r'[a-zA-Z]+', query)
        english_word_count = len(english_words)
        
        # 统计数字和符号
        numbers_symbols = re.findall(r'[\d\W]', query)
        total_chars = len(query)
        
        # 判断主要语言类型
        if chinese_count > 0 and english_word_count > 0:
            # 混合查询，不强制切换
            return False
            
        if chinese_count > 0 and english_word_count == 0:
            # 纯中文查询，4个字以下强制全文检索
            if chinese_count <= 4:
                return True
                
        if english_word_count > 0 and chinese_count == 0:
            # 纯英文查询，少于3个单词强制全文检索
            if english_word_count < 3:
                return True
                
        # 纯数字、符号或特殊字符
        if chinese_count == 0 and english_word_count == 0 and len(numbers_symbols) > 0:
            return True
            
        # 查询过短
        if total_chars < 3:
            return True
            
        return False
    
    def calculate_time_weight(self, published_time, search_type, time_weight_factor=0.2):
        """
        计算基于发布时间的权重分数（分层权重系统）
        
        根据文章发布时间计算时间权重，越新的文章权重越高。
        采用分层权重系统，不同时间段有不同的基础权重：
        - 1个月内：权重1.0（最新）
        - 3个月内：权重0.9（很新）
        - 1年内：权重0.7（较新）
        - 3年内：权重0.5（一般）
        - 5年内：权重0.3（较旧）
        - 10年内：权重0.1（很旧）
        - 10年以上：权重0.05（最旧）
        
        Args:
            published_time (int): 文章发布时间戳（毫秒）
            search_type (str): 搜索类型，影响权重范围
                - "全文检索": 0-50分范围
                - "语义搜索": 0-0.5分范围
            time_weight_factor (float): 时间权重因子，固定为0.2
            
        Returns:
            float: 计算得出的时间权重分数
            
        Note:
            时间权重会根据搜索类型调整最终分数范围，确保与相关性分数协调
        """
        if not published_time or time_weight_factor == 0:
            return 0.0

        try:
            current_time = int(time.time() * 1000)  # 当前时间戳（毫秒）
            age_ms = current_time - published_time
            age_days = age_ms / (24 * 3600 * 1000)

            # 分层时间权重系统
            if age_days <= 0:
                # 未来时间或当天
                base_weight = 1.0
            elif age_days <= 30:
                # 1个月内：最高权重
                base_weight = 1.0
            elif age_days <= 90:
                # 3个月内：高权重
                base_weight = 0.9
            elif age_days <= 365:
                # 1年内：中高权重
                base_weight = 0.7
            elif age_days <= 365 * 3:
                # 3年内：中等权重
                base_weight = 0.5
            elif age_days <= 365 * 5:
                # 5年内：中低权重
                base_weight = 0.3
            elif age_days <= 365 * 10:
                # 10年内：低权重
                base_weight = 0.1
            else:
                # 10年以上：最低权重
                base_weight = 0.05

            # 根据搜索类型调整权重范围
            if search_type == "全文检索":
                # 全文检索：0-25分范围
                max_weight = 50.0
                final_weight = base_weight * max_weight * time_weight_factor
            else:
                # 语义搜索：保持原有范围，0-0.15分
                max_weight = 0.5
                final_weight = base_weight * max_weight * time_weight_factor

            return final_weight

        except Exception as e:
            return 0.0
    

     
    def generate_query_vector(self, query: str, max_retries: int = 3) -> Optional[List[float]]:
        """
        使用多API密钥将查询文本转换为向量（支持负载均衡）
        
        调用有道的文本嵌入API，将用户查询转换为768维的向量表示，
        用于后续的向量相似度搜索。支持多API密钥轮询和故障转移。
        
        Args:
            query (str): 需要向量化的查询文本
            max_retries (int): 最大重试次数，默认3次
            
        Returns:
            Optional[List[float]]: 768维向量列表，失败时返回None
            
        Note:
            - 使用有道bce-embedding-base_v1模型
            - 支持中英文混合文本
            - 支持多API密钥负载均衡
            - 包含指数退避重试机制
            - 自动处理API密钥失效和切换
        """
        url = "https://api.siliconflow.cn/v1/embeddings"
        
        payload = {
            "model": "netease-youdao/bce-embedding-base_v1",
            "input": query,
            "encoding_format": "float"
        }
        
        for attempt in range(max_retries):
            # 获取当前可用的API密钥
            api_key = self.api_manager.get_next_api_key()
            if not api_key:
                # 没有可用的API密钥
                return None
            
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            try:
                response = requests.post(url, json=payload, headers=headers, timeout=10)
                response.raise_for_status()
                
                data = response.json()
                vector = data.get('data', [{}])[0].get('embedding')
                
                if vector:
                    return vector
                else:
                    return None
                    
            except requests.exceptions.Timeout:
                # 请求超时，尝试下一个密钥
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                    
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:  # 速率限制
                    # 标记当前密钥为失效，切换到下一个
                    self.api_manager.mark_key_failed(api_key, '429')
                    time.sleep(1)  # 短暂等待后重试
                elif e.response.status_code == 401:  # 认证失败
                    # 标记当前密钥为失效
                    self.api_manager.mark_key_failed(api_key, '401')
                else:
                    return None
                    
            except Exception as e:
                return None
        
        return None
    
    def get_api_stats(self) -> dict:
        """
        获取API密钥使用统计
        
        Returns:
            dict: 包含总数、可用数、失效数的统计信息
        """
        return self.api_manager.get_stats()



    def build_time_filter_expr(self, time_type: int) -> Optional[str]:
        """
        构建Milvus时间筛选表达式（优化后）
        
        修改后的时间筛选逻辑：
        - 0: 所有时间（但排除未来时间的定时发布数据）
        - 1: 1天内（最近24小时）
        - 2: 一周内（最近7天）
        - 3: 一月内（最近30天）
        - 4: 一年内（最近365天）
        
        注意：所有查询都会添加 published_time <= 当前时间 的条件，
        以排除定时发布的未来数据。
        
        Args:
            time_type (int): 时间筛选类型
                
        Returns:
            Optional[str]: Milvus查询表达式，如 "(published_time >= 1640995200000) AND (published_time <= 1640995200000)"
                          None表示不进行时间筛选
        """
        current_time = int(time.time() * 1000)  # 当前时间戳（毫秒）
        
        # 基础条件：发布时间不能超过当前时间（排除定时发布的未来数据）
        base_condition = f"published_time <= {current_time}"
        
        if time_type == 0:  # 所有时间
            return base_condition  # 只限制不能是未来时间
            
        # 构建时间范围条件
        time_range_condition = None
        if time_type == 1:  # 1天内
            day_ago = current_time - (1 * 24 * 3600 * 1000)
            time_range_condition = f"published_time >= {day_ago}"
        elif time_type == 2:  # 一周内
            week_ago = current_time - (7 * 24 * 3600 * 1000)
            time_range_condition = f"published_time >= {week_ago}"
        elif time_type == 3:  # 一月内
            month_ago = current_time - (30 * 24 * 3600 * 1000)
            time_range_condition = f"published_time >= {month_ago}"
        elif time_type == 4:  # 一年内
            year_ago = current_time - (365 * 24 * 3600 * 1000)
            time_range_condition = f"published_time >= {year_ago}"
        else:
            return base_condition  # 无效值，只限制不能是未来时间
        
        # 组合基础条件和时间范围条件
        if time_range_condition:
            return f"({time_range_condition}) AND ({base_condition})"
        else:
            return base_condition
    
    def deduplicate_results(self, results: list) -> list:
        """
        对搜索结果按entity_id和entity_type去重，保持相似度排序
        
        Args:
            results (list): 原始搜索结果列表，每个元素包含entity_id、entity_type、score等字段
        
        Returns:
            list: 去重后的结果列表，保持按score降序排列
        """
        if not results:
            return results
        
        # 使用字典记录已见过的(entity_id, entity_type)组合
        seen_entities = set()
        deduplicated_results = []
        
        # 遍历已排序的结果（按score降序）
        for result in results:
            entity_key = (result.get("entity_id"), result.get("entity_type"))
            
            # 如果这个实体组合还没见过，则保留（保持最高分数的记录）
            if entity_key not in seen_entities:
                seen_entities.add(entity_key)
                deduplicated_results.append(result)
        
        return deduplicated_results
    
    def __init__(self):
        """
        初始化MedSci搜索服务实例
        
        创建搜索服务对象，准备执行各种类型的搜索操作。
        包括Milvus连接管理器和多API密钥管理器的初始化。
        """
        self.connection_manager = MilvusConnectionManager()
        self.api_manager = MultiApiKeyManager()
    
    def validate_and_truncate_query(self, query: str) -> str:
        """
        验证并截取查询字符串
        
        为了避免向量化API的token限制，将查询字符串限制在500字符以内。
        超过500字符直接截取，提升性能。
        
        Args:
            query (str): 原始查询字符串
            
        Returns:
            str: 处理后的查询字符串
        """
        if not query:
            return ""
        
        query = query.strip()
        
        # 限制最大长度为500字符，直接截取
        if len(query) > 500:
            query = query[:500]
        
        return query

    def search(self, request: SearchRequest) -> Dict[str, Any]:
        """
        执行智能搜索的主入口方法
        
        根据搜索请求参数，自动选择最佳的检索方式（语义搜索或全文检索），
        并返回排序后的搜索结果。支持时间筛选、权重计算等高级功能。
        
        搜索流程：
        1. 连接Milvus数据库
        2. 检查集合是否存在
        3. 智能判断检索方式
        4. 构建筛选条件
        5. 执行搜索（语义搜索或全文检索）
        6. 应用时间权重
        7. 排序和限制结果数量
        
        Args:
            request (SearchRequest): 搜索请求参数对象
            
        Returns:
            Dict[str, Any]: 搜索结果，格式为：
                {
                    "code": 200,  # 状态码
                    "message": "搜索成功",  # 消息
                    "data": [  # 结果列表
                        {
                            "entity_id": "123",
                            "entity_type": "article", 
                            "score": 0.95,
                            "published_time": 1640995200000
                        }
                    ]
                }
                
        Note:
            - 自动选择检索方式：汉字≤4个用全文检索，否则用语义搜索
            - 默认启用时间权重，权重因子固定为0.2
            - 支持多种时间范围筛选和排序方式
        """
        try:
            # 步骤0：参数验证（优化后）
            if not request.q or not request.q.strip():
                return CommonResult.error(message="查询内容不能为空", data=[])
            
            # 验证并截取查询字符串
            request.q = self.validate_and_truncate_query(request.q)
            if not request.q:
                return CommonResult.error(message="查询内容无效", data=[])
            
            if request.limit <= 0 or request.limit > 500:
                return CommonResult.error(message="limit参数必须在1-500之间", data=[])
            
            # 修改后的time_type验证：支持0-4
            if request.time_type not in [0, 1, 2, 3, 4]:
                return CommonResult.error(message="time_type参数有误，支持0-4", data=[])
            
            if request.sort_type not in [1, 2]:
                return CommonResult.error(message="sort_type参数有误", data=[])
            
            if request.search_type not in [1, 2]:
                return CommonResult.error(message="search_type参数有误", data=[])
            
            # 步骤1：建立Milvus数据库连接（使用连接管理器）
            collection = self.connection_manager.get_collection()
            if collection is None:
                return CommonResult.error(message="连接Milvus数据库失败", data=[])
            
            # 步骤3：智能选择检索方式
            # 根据查询内容特征自动判断：汉字≤4个用全文检索，否则用语义检索
            use_fulltext = self.should_use_fulltext_search(request.q)
            
            # 步骤4：构建向量类型筛选条件
            if request.search_type == 1:  # 全文搜索模式
                expr = "vector_type in [1, 2, 3]"  # 搜索标题、内容、摘要三种类型
            else:  # 标题搜索模式
                expr = "vector_type in [1]"  # 仅搜索标题类型
            
            # 步骤5：添加模块类型筛选条件（根据module参数过滤entity_type）
            if request.module and request.module.strip():
                module_expr = f"entity_type == '{request.module.strip()}'"
                expr = f"({expr}) AND ({module_expr})"
            
            # 步骤6：添加时间范围筛选条件
            time_expr = self.build_time_filter_expr(request.time_type)
            if time_expr:
                expr = f"({expr}) AND ({time_expr})"  # 组合向量类型、模块类型和时间筛选条件

            # 步骤7：配置时间权重参数
            enable_time_weight = True  # 默认启用时间权重功能
            
            # 步骤8：调整搜索数量以支持去重和时间权重重排序
            # 使用limit*1.5进行查询以确保去重后有足够的结果
            search_limit = round(request.limit * 1.5)
            
            # 步骤9：初始化搜索结果容器
            results = []  # 存储最终搜索结果
            output_fields = ["entity_id", "entity_type", "published_time"]  # 需要返回的字段
            
            if use_fulltext:
                # 执行BM25全文检索分支
                
                # 记录检索开始时间
                start_time = time.time()
                
                # 调用Milvus稀疏向量搜索（BM25）
                search_results = collection.search(
                    data=[request.q],              # 查询文本
                    anns_field="sparse",           # 使用稀疏向量字段
                    param={"metric_type": "BM25"}, # BM25相似度计算
                    limit=search_limit,            # 搜索结果数量
                    expr=expr,                     # 筛选条件
                    output_fields=output_fields,    # 返回字段
                )
                
                # 记录检索耗时
                search_time = time.time() - start_time
                
                # 步骤9：处理全文检索结果并应用时间权重
                if search_results and len(search_results) > 0:
                    for hit in search_results[0]:
                        bm25_score = float(hit.distance)  # BM25相关性分数
                        published_time = hit.entity.get("published_time")
                        
                        # 计算并应用时间权重
                        if enable_time_weight and published_time:
                            time_weight = self.calculate_time_weight(published_time, "全文检索", 0.2)
                            final_score = bm25_score + time_weight  # 相关性分数 + 时间权重
                        else:
                            final_score = bm25_score
                        
                        # 构建结果对象
                        results.append({
                            "entity_id": hit.entity.get("entity_id"),
                            "entity_type": hit.entity.get("entity_type"),
                            "score": final_score,
                            "published_time": published_time
                        })
                    
                    # 步骤10：根据 sort_type 参数进行排序
                    if request.sort_type == 1:  # 相关度排序
                        results.sort(key=lambda x: x["score"], reverse=True)  # 按分数降序
                    elif request.sort_type == 2:  # 发布时间倒序
                        results.sort(key=lambda x: x["published_time"] or 0, reverse=True)  # 按时间降序
                    
                    # 步骤11：对结果按entity_id和entity_type去重（保持相似度排序）
                    results = self.deduplicate_results(results)
                    
                    # 步骤12：限制返回数量
                    # 如果去重后结果数量大于limit，则按分数从高到低取limit数量
                    # 如果去重后结果数量小于等于limit，则全部返回
                    if len(results) > request.limit:
                        results = results[:request.limit]
                    # 如果结果数量小于等于limit，保持原有结果不变
                    
            else:
                # 执行向量语义检索分支
                
                # 步骤9：生成查询文本的向量表示
                query_vector = self.generate_query_vector(request.q)
                if not query_vector:
                    return CommonResult.error(message="生成查询向量失败", data=[])
                
                # 步骤10：执行向量相似度搜索
                start_time = time.time()
                
                # 调用Milvus密集向量搜索（余弦相似度）
                search_results = collection.search(
                    data=[query_vector],                                    # 查询向量
                    anns_field="vector",                                   # 使用密集向量字段
                    param={"metric_type": "COSINE", "params": {"nprobe": 16}}, # 余弦相似度，探测30个聚类
                    limit=search_limit,                                    # 搜索结果数量
                    expr=expr,                                             # 筛选条件
                    output_fields=output_fields                           # 返回字段
                )
                
                # 记录检索耗时
                search_time = time.time() - start_time
                
                # 步骤11：处理语义检索结果并应用时间权重
                if search_results and len(search_results) > 0:
                    for hit in search_results[0]:
                        cosine_score = float(hit.distance)  # 余弦相似度分数
                        published_time = hit.entity.get("published_time")
                        
                        # 计算并应用时间权重
                        if enable_time_weight and published_time:
                            time_weight = self.calculate_time_weight(published_time, "语义搜索", 0.3)
                            final_score = cosine_score + time_weight  # 相似度分数 + 时间权重
                        else:
                            final_score = cosine_score
                        
                        # 构建结果对象
                        results.append({
                            "entity_id": hit.entity.get("entity_id"),
                            "entity_type": hit.entity.get("entity_type"),
                            "score": final_score,
                            "published_time": published_time
                        })
                    
                    # 步骤12：根据 sort_type 参数进行排序
                    if request.sort_type == 1:  # 相关度排序
                        results.sort(key=lambda x: x["score"], reverse=True)  # 按分数降序
                    elif request.sort_type == 2:  # 发布时间倒序
                        results.sort(key=lambda x: x["published_time"] or 0, reverse=True)  # 按时间降序
                    
                    # 步骤13：对结果按entity_id和entity_type去重（保持相似度排序）
                    results = self.deduplicate_results(results)
                    
                    # 步骤14：限制返回数量
                    # 如果去重后结果数量大于limit，则按分数从高到低取limit数量
                    # 如果去重后结果数量小于等于limit，则全部返回
                    if len(results) > request.limit:
                        results = results[:request.limit]
                    # 如果结果数量小于等于limit，保持原有结果不变
            
            # 步骤15：格式化并返回搜索结果
            # 返回数组格式，每个元素包含entity_id和entity_type，支持混合类型结果
            formatted_results = []
            for item in results:
                formatted_results.append({
                    "entity_id": item["entity_id"],
                    "entity_type": item["entity_type"]
                })
            
            return CommonResult.success(data=formatted_results, message="搜索成功")
            
        except Exception as e:
            # 捕获并记录所有搜索过程中的异常
            return CommonResult.error(message=f"搜索失败: {str(e)}", data=[])

# 创建全局搜索服务实例，供外部调用
search_service = SearchService()